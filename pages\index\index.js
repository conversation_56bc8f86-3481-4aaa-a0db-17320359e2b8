// index.js
import bleTool from '../../SUPVANAPIT50PRO/BLETool.js'
import bleToothManage from '../../SUPVANAPIT50PRO/BLEToothManage.js'
import constants from '../../SUPVANAPIT50PRO/Constants.js'
Page({
  data: {
    blueList: [{}], //搜索到的蓝牙列表
    templateWidth: 560,//模版宽度
    templateHeight: 302,//模版高度
    barCodeWidth: 214,//条码宽度
    barCodeHeight: 72,//条码高度
    qrCodeWidth: 20,//二维码宽度
    qrCodeHeight: 20,//二维码高度
    pixelRatio: 2,//像素比
    canvasBarCode: null,//条码canvas
    canvasText: null,//文本canvas
    printCopies: 0,//打印份数
    printNum: 0,//当前打印的份数
    previewImagePath: '', // 存储预览图路径
    showPreview: false,   // 控制预览图显示状态
    showEditTemplate: false, // 控制编辑模版弹窗显示状态
    editPreviewImagePath: '', // 编辑模版预览图路径
    // 可编辑的模版内容
    editableContent: {
      productName: '食品A食品A食品A', // 品名
      operator: 'ChocoTong', // 操作人
      date: '2025-06-12' // 日期
    },
    //字模数据组成格式
    PageObject: [
      {
        "Width": 40,
        "Height": 30,
        "Rotate": 0,
        "Copies": 1,
        "Density": 2,
        "HorizontalNum": 0,
        "VerticalNum": 0,
        "PaperType": 1,
        "Gap": 3,
        "Speed": 25,
        // "SavePaper": false,
        "FirstCut": 1,
        "CutType": 1,
        "DeviceSn": 'T0145B',
        "ImageWidth": 40,//图片宽（单位mm）
        "ImageHeight": 30,//图片高（单位mm)
        "PreviewPath": 'https://img.alicdn.com/imgextra/i2/3907484842/O1CN01D2qTCv1ldghCs5vzc_!!3907484842.png',//预览图的图
        "DrawObjects": [{
          "AntiColor": false,
          "X": 2,
          "Y": 7,
          "Width": 35,
          "Height": 4,
          "Content": "品名|_____________________",
          "FontName": "HarmonyOS Sans SC",
          "FontStyle": 2,
          "FontSize": "3",
          "Format": "TEXT",
          "Orientation": 0
        },{
          "AntiColor": false,
          "X": 9,
          "Y": 5.5,
          "Width": 28,
          "Height": 4,
          "Content": "食品A食品A食品A",
          "FontName": "HarmonyOS Sans SC",
          "FontStyle": 2,
          "FontSize": "5",
          "Format": "TEXT",
          "Orientation": 0
        },{
          "AntiColor": false,
          "X": 2,
          "Y": 15,
          "Width": 35,
          "Height": 4,
          "Content": "操作人|______________________",
          "FontName": "HarmonyOS Sans SC",
          "FontStyle": 2,
          "FontSize": "3",
          "Format": "TEXT",
          "Orientation": 0
        },{
          "AntiColor": false,
          "X": 12,
          "Y": 13.5,
          "Width": 20,
          "Height": 4,
          "Content": "戴经理",
          "FontName": "HarmonyOS Sans SC",
          "FontStyle": 2,
          "FontSize": "5",
          "Format": "TEXT",
          "Orientation": 0
        },{
          "AntiColor": false,
          "X": 2,
          "Y": 23,
          "Width": 35,
          "Height": 4,
          "Content": "日期|______________________",
          "FontName": "HarmonyOS Sans SC",
          "FontStyle": 2,
          "FontSize": "3",
          "Format": "TEXT",
          "Orientation": 0
        },{
          "AntiColor": false,
          "X": 9,
          "Y": 21.5,
          "Width": 23,
          "Height": 4,
          "Content": "2025-06-12",
          "FontName": "HarmonyOS Sans SC",
          "FontStyle": 2,
          "FontSize": "5",
          "Format": "TEXT",
          "Orientation": 0
        },
        // {
        //   "AntiColor": false,
        //   "X": 5,
        //   "Y": 28,
        //   "Width": 30,
        //   "Height": 4,
        //   "Content": "厂家:天方药业有限公司",
        //   "FontName": "HarmonyOS Sans SC",
        //   "FontStyle": 2,
        //   "FontSize": "3",
        //   "Format": "TEXT",
        //   "Orientation": 0
        // },
        // {
        //   "AntiColor": false,
        //   "X": 10,
        //   "Y": 22,
        //   "Width": 11,
        //   "Height": 3,
        //   "Content": "盒",
        //   "FontName": "HarmonyOS Sans SC",
        //   "FontStyle": 2,
        //   "FontSize": "3",
        //   "Format": "TEXT",
        //   "Orientation": 0
        // },
        // {
        //   "AntiColor": false,
        //   "X": 24,
        //   "Y": 21.5,
        //   "Width": 11,
        //   "Height": 3,
        //   "Content": "药品",
        //   "FontName": "HarmonyOS Sans SC",
        //   "FontStyle": 2,
        //   "FontSize": "3",
        //   "Format": "TEXT",
        //   "Orientation": 0
        // },
        // {
        //   "AntiColor": false,
        //   "X": 23,
        //   "Y": 0,
        //   "Width": 40,
        //   "Height": 7,
        //   "Content": "健康大药房（仅用于试用）",
        //   "FontName": "HarmonyOS Sans SC",
        //   "FontStyle": 2,
        //   "FontSize": "5",
        //   "Format": "TEXT",
        //   "Orientation": 0
        // },
        // {
        //   "AntiColor": false,
        //   "X": 47,
        //   "Y": 27,
        //   "Width": 30,
        //   "Height": 7,
        //   "Content": "23.275",
        //   "FontName": "HarmonyOS Sans SC",
        //   "FontStyle": 2,
        //   "FontSize": "7",
        //   "Format": "TEXT",
        //   "Orientation": 0
        // }
        ]
      },
      // {
      //   "Width": 50,
      //   "Height": 50,
      //   "Rotate": 1,
      //   "Copies": 1,
      //   "Density": 4,
      //   "HorizontalNum": 0,
      //   "VerticalNum": 0,
      //   "PaperType": 1,
      //   "Gap": 8,
      //   "DeviceSn": 'T0100A2305163367',
      //   "DrawObjects": [{
      //     "AntiColor": false,
      //     "X": 19,
      //     "Y": 9,
      //     "Width": 30,
      //     "Height": 7,
      //     "Content": "烟草1",
      //     "FontName": "HarmonyOS Sans SC",
      //     "FontStyle": 7,
      //     "FontSize": "4",
      //     "Format": "TEXT"
      //   },
      //   {
      //     "AntiColor": false,
      //     "X": 10,
      //     "Y": 20,
      //     "Width": 10,
      //     "Height": 7,
      //     "Content": "地址1",
      //     "FontName": "HarmonyOS Sans SC",
      //     "FontStyle": 2,
      //     "FontSize": "5",
      //     "Format": "TEXT"
      //   },
      //   {
      //     "AntiColor": false,
      //     "X": 29,
      //     "Y": 17,
      //     "Width": 12,
      //     "Height": 10,
      //     "Content": "价格1",
      //     "FontName": "HarmonyOS Sans SC",
      //     "FontStyle": 2,
      //     "FontSize": "8",
      //     "Format": "TEXT"
      //   }
      //   ]
      // }
    ],
    PageImageObject: [{
      "Width": 50,
      "Height": 30,
      "Rotate": 1,
      "Copies": 1,
      "Density": 15,
      "HorizontalNum": 0,
      "VerticalNum": 0,
      "PaperType": 1,
      "Gap": 8,
      "DeviceSn": 'T0145B',
      "ImageUrl": 'https://sjpt.issas.ac.cn/profile/upload/Template/202502271300003.jpg',
      "ImageWidth": 50,//图片宽（单位mm）
      "ImageHeight": 30,//图片高（单位mm）
    },
    {
      "Width": 48,
      "Height": 30,
      "Rotate": 1,
      "Copies": 1,
      "Density": 15,
      "HorizontalNum": 0,
      "VerticalNum": 0,
      "PaperType": 1,
      "Gap": 8,
      "DeviceSn": 'T0145B',
      "ImageUrl": 'https://sjpt.issas.ac.cn/profile/upload/Template/202502271300003.jpg',
      "ImageWidth": 50,//图片宽（单位mm）
      "ImageHeight": 30,//图片高（单位mm）
    },
    {
      "Width": 48,
      "Height": 30,
      "Rotate": 1,
      "Copies": 1,
      "Density": 15,
      "HorizontalNum": 0,
      "VerticalNum": 0,
      "PaperType": 1,
      "Gap": 8,
      "DeviceSn": 'T0145B',
      "ImageUrl": 'https://sjpt.issas.ac.cn/profile/upload/Template/202502271300003.jpg',
      "ImageWidth": 50,//图片宽（单位mm）
      "ImageHeight": 30,//图片高（单位mm）
    },
    ],
  },
  /**
   * 页面加载时的生命周期函数
   * 设置导航栏标题，初始化像素比、条码 canvas 和文本 canvas
   * @CallIn 页面初次加载时自动调用
   * @CallOut 无
   */
  onLoad() {
    wx.setNavigationBarTitle({
      title: '微信小程序SDK客户版本',
    })
    this.setData({
      pixelRatio: wx.getWindowInfo().pixelRatio,
      canvasText: wx.createCanvasContext('Canvas', this),
      canvasBarCode: wx.createSelectorQuery().in(this),
    })

  },
  /**
   * 
   * 启动搜索蓝牙并将搜索到的蓝牙信息存到集合 blueList 里面
   * @CallIn index.wxml 中触发点击开始蓝牙搜索按钮点击事件
   * @CallOut bleTool.scanBleDeviceList
   */
  clickStartScanBleDevice() {
    const that = this
    that.data.blueList = []//清空集合
    bleTool.scanBleDeviceList((res)=>{
      console.log('搜索到的蓝牙设备:', res);
      if (res.ResultCode == 0) {
        if (res && res.ResultValue && res.ResultValue.devices[0]) {
          that.data.blueList.push(res.ResultValue.devices[0])
          that.setData({
            //blueList: [...[], res.ResultValue.devices[0]]
            blueList:that.data.blueList
          })
        }
      }
    }).catch(error => {
      console.log('搜索蓝牙设备失败:', error);
    })
  },
  /**
   * 停止搜索蓝牙
   * CallIn:index.wxml 中触发点击停止蓝牙搜索按钮点击事件
   * CallOut:BLETool.stopScanBleDevices
   */
  clickStopScanBleDevices() {
    bleTool.stopScanBleDevices().then(res => {
      console.log(res)
      wx.showToast({
        title: '停止搜索蓝牙设备成功',
        icon: 'none'
      })
    }).catch(error => {
      console.log('停止搜索蓝牙设备失败:', error);
    })
  },
  /**
   * 连接蓝牙设备
   * @param {object} elment 事件对象(包含触发事件时的相关信息)
   * @CallIn index.wxml 中触发点击搜索到的蓝牙名称按钮点击事件
   * @CallOut bleTool.connectBleDevice
   */
  clickConnectBleDevice(elment) {
    const that = this
    const index = elment.currentTarget.dataset.index//获取当前点击蓝牙列表的下标
    const deviceName = that.data.blueList[index].name//获取当前点击蓝牙列表的名字
    that.data.PageObject.forEach(objectdata => objectdata.DeviceSn = deviceName)
    that.data.PageImageObject.forEach(objectdataImg => objectdataImg.DeviceSn = deviceName)
    bleTool.connectBleDevice(that.data.blueList[index]).then(res => {
      console.log(res)
    }).catch(error => {
      console.log(error);
    })
  },

  /**
   * 断开蓝牙
   * @CallIn index.wxml 中触发点击断开蓝牙按钮点击事件
   * @CallOut bleTool.disconnectBleDevice
   */
  clickDisconnectBleDevice() {
    bleTool.disconnectBleDevice().then(res => {
      console.log(res);
    }).catch(err => {
      console.log(err);
    })
  },
  /**
   * 打印示例模板
   * @CallIn index.wxml 中触发点击开始打印示例字模的按钮点击事件
   * @CallOut BLEToothManage.doPrintMatrix
   */
  clickStartPrintTemplatePageObjectLocal() {
    bleToothManage.doPrintMatrix(this.data.canvasText, this.data.PageObject, this.data.canvasBarCode, res => {
      console.log('数据回调', res)
      if (res.ResultCode == constants.globalResultCode.ResultCode100) {
        let resultValue = res.ResultValue
        this.setData({
          templateWidth: resultValue.width,
          templateHeight: resultValue.height,
          barCodeWidth: resultValue.barcodeWidth,
          barCodeHeight: resultValue.barcodeHeight,
          qrCodeWidth: resultValue.qrcodeWidth,
          qrCodeHeight: resultValue.qrcodeHeight,
        })
      }

    }).catch(error => { console.log('打印失败', error) })
  },
  /**
   * 打印图片
   * @CallIn index.wxml 中触发点击开启打印图片的按钮点击事件
   * @CallOut BLEToothManage.js 里面 doPrintImage
   */
  clickStartPrintTemplatePageImageLocal() {
    console.log('打印图片')
    bleToothManage.doPrintImage(this.data.canvasText, this.data.PageImageObject, res => {
      console.log('打印图片回调', res)
      if (res.ResultCode == 100) {
        let resultValue = res.ResultValue
        this.setData({
          templateWidth: resultValue.width,
          templateHeight: resultValue.height,
        })
      }
    }).catch(error => {
      console.log('打印图片失败', error)
    })
  },
  /**
   * 绘制预览图
   * @CallIn index.wxml 中触发点击开始绘制预览图的按钮点击事件
   * @CallOut BLEToothManage.js 里面 doDrawPreview
   */
  clickStartDrawPreview() {
    console.log('预览图片')
    const that = this
    bleToothManage.doDrawPreview(this.data.canvasText, this.data.PageObject, this.data.canvasBarCode, res => {
      if (res.ResultCode == constants.globalResultCode.ResultCode100) {
        console.log('预览图尺寸:', res);
        let resultValue = res.ResultValue
        this.setData({
          templateWidth: resultValue.width,
          templateHeight: resultValue.height,
          barCodeWidth: resultValue.barcodeWidth,
          barCodeHeight: resultValue.barcodeHeight,
          qrCodeWidth: resultValue.qrcodeWidth,
          qrCodeHeight: resultValue.qrcodeHeight,
        })
      } else if (res.ResultCode == constants.globalResultCode.ResultCodeSuccess) {
        console.log('预览图集合:', res);
        // 保存预览图路径并显示
        if (res.ResultValue && res.ResultValue.previewList && res.ResultValue.previewList.length > 0) {
          that.setData({
            previewImagePath: res.ResultValue.previewList[0],
            showPreview: true
          })
        }
      }
    }).catch(error => { console.error(error) })
  },
  /**
   * 终止打印
   * @CallIn index.wxml 中触发点击终止打印的按钮点击事件
   * @CallOut BLEToothManage.js 里面 onStopPrint
   */
  clickStopPrint() {
    bleTool.stopPrint(res => {
      console.log('终止打印回调', res)
    }).catch(error => { console.error(error) })
  },
  /**
   * 输入打印份数
   * @param {object} e 事件对象，包含输入框的输入值
   * @CallIn index.wxml 中输入框输入内容时触发
   * @CallOut 无
   */
  startEnterPrintCopies(e) {
    console.log('打印份数', e.detail.value);
    const copies = e.detail.value
    this.setData({
      'PageObject[0].Copies': copies,
      'PageImageObject[0].Copies': copies
    });
  },
  /**
   * 关闭预览图
   */
  closePreview() {
    this.setData({
      showPreview: false
    })
  },

  /**
   * 打开编辑模版弹窗
   */
  openEditTemplate() {
    this.setData({
      showEditTemplate: true
    })
    // 立即生成预览图
    this.generateEditPreview()
  },

  /**
   * 关闭编辑模版弹窗
   */
  closeEditTemplate() {
    this.setData({
      showEditTemplate: false,
      editPreviewImagePath: ''
    })
  },

  /**
   * 输入品名
   */
  onProductNameInput(e) {
    this.setData({
      'editableContent.productName': e.detail.value
    })
    // 实时更新预览图
    this.generateEditPreview()
  },

  /**
   * 输入操作人
   */
  onOperatorInput(e) {
    this.setData({
      'editableContent.operator': e.detail.value
    })
    // 实时更新预览图
    this.generateEditPreview()
  },

  /**
   * 输入日期
   */
  onDateInput(e) {
    this.setData({
      'editableContent.date': e.detail.value
    })
    // 实时更新预览图
    this.generateEditPreview()
  },

  /**
   * 生成编辑模版的预览图
   */
  generateEditPreview() {
    const that = this
    // 创建一个临时的PageObject，使用编辑的内容
    const tempPageObject = JSON.parse(JSON.stringify(this.data.PageObject))

    // 更新DrawObjects中的内容
    if (tempPageObject[0] && tempPageObject[0].DrawObjects) {
      tempPageObject[0].DrawObjects[1].Content = this.data.editableContent.productName
      tempPageObject[0].DrawObjects[3].Content = this.data.editableContent.operator
      tempPageObject[0].DrawObjects[5].Content = this.data.editableContent.date
    }

    // 调用预览生成方法
    bleToothManage.doDrawPreview(this.data.canvasText, tempPageObject, this.data.canvasBarCode, res => {
      if (res.ResultCode == constants.globalResultCode.ResultCode100) {
        console.log('编辑预览图尺寸:', res);
        let resultValue = res.ResultValue
        this.setData({
          templateWidth: resultValue.width,
          templateHeight: resultValue.height,
          barCodeWidth: resultValue.barcodeWidth,
          barCodeHeight: resultValue.barcodeHeight,
          qrCodeWidth: resultValue.qrcodeWidth,
          qrCodeHeight: resultValue.qrcodeHeight,
        })
      } else if (res.ResultCode == constants.globalResultCode.ResultCodeSuccess) {
        console.log('编辑预览图集合:', res);
        // 保存编辑预览图路径
        if (res.ResultValue && res.ResultValue.previewList && res.ResultValue.previewList.length > 0) {
          that.setData({
            editPreviewImagePath: res.ResultValue.previewList[0]
          })
        }
      }
    }).catch(error => { console.error('生成编辑预览图失败:', error) })
  },

  /**
   * 打印编辑后的模版
   */
  printEditedTemplate() {
    // 创建一个临时的PageObject，使用编辑的内容
    const tempPageObject = JSON.parse(JSON.stringify(this.data.PageObject))

    // 更新DrawObjects中的内容
    if (tempPageObject[0] && tempPageObject[0].DrawObjects) {
      tempPageObject[0].DrawObjects[1].Content = this.data.editableContent.productName
      tempPageObject[0].DrawObjects[3].Content = this.data.editableContent.operator
      tempPageObject[0].DrawObjects[5].Content = this.data.editableContent.date
    }

    // 调用打印方法
    bleToothManage.doPrintMatrix(this.data.canvasText, tempPageObject, this.data.canvasBarCode, res => {
      console.log('编辑模版打印回调', res)
      if (res.ResultCode == constants.globalResultCode.ResultCode100) {
        let resultValue = res.ResultValue
        this.setData({
          templateWidth: resultValue.width,
          templateHeight: resultValue.height,
          barCodeWidth: resultValue.barcodeWidth,
          barCodeHeight: resultValue.barcodeHeight,
          qrCodeWidth: resultValue.qrcodeWidth,
          qrCodeHeight: resultValue.qrcodeHeight,
        })
        wx.showToast({
          title: '打印成功',
          icon: 'success'
        })
      }
    }).catch(error => {
      console.log('编辑模版打印失败', error)
      wx.showToast({
        title: '打印失败',
        icon: 'error'
      })
    })
  },

  /**
   * 跳转到打印机页面
   */
  goToPrinterPage() {
    wx.navigateTo({
      url: '/pages/printer/printer'
    })
  },
})




