import printingControl from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0050\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0043\u006F\u006E\u0074\u0072\u006F\u006C\u002E\u006A\u0073";import pr from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0050\u0041\u0047\u0045\u005F\u0052\u0045\u0047\u005F\u0042\u0049\u0054\u0053\u002E\u006A\u0073";import lzma from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u006C\u007A\u006D\u0061\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import imageDataUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u0055\u0074\u0069\u006C\u0073";import supVanPrintUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import bleToothManage from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065";import bleTool from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C";import constants from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";export default{'\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068':50,'\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074':30,'\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065':8,'\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061':[],"encodeList":[],'\u006F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065':0,"imageDataListAll":[],'\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074':0,'\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065':false,"bufferPackageList":[],"objectData":null,'\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065':'',"callback":null,async initEncodeData(objectData,imageRgbaData){try{var _0x4d2c=(895231^895222)+(261274^261277);const that=this;_0x4d2c=(973562^973562)+(449906^449904);that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']=objectData;that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061']=imageRgbaData;that['\u006F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065']=objectData['\u0069\u0073\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E']?517681^517680:386202^386202;await that['\u0069\u006E\u0069\u0074\u0069\u0061\u006C\u0069\u007A\u0065\u0044\u0061\u0074\u0061']();await that['\u0069\u006E\u0069\u0074\u004C\u005A\u004D\u0041\u0044\u0061\u0074\u0061']();await that['\u0064\u006F\u0050\u0072\u0069\u006E\u0074']();}catch(error){throw error;}},initializeData(){const that=this;that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']=[];printingControl['\u0063\u006C\u0065\u0061\u006E\u0050\u0063']();imageDataUtils['\u0067\u0065\u0074\u0043\u006C\u0065\u0061\u006E']();pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']==(895087^895087)){that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']=849463^849462;}let rotateAngle=(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']-(668353^668355))*(372402^372456);let object={"imageRgbaData":that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061'],'\u0057\u0069\u0064\u0074\u0068':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0048\u0065\u0069\u0067\u0068\u0074':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0072\u006F\u0074\u0061\u0074\u0065\u0041\u006E\u0067\u006C\u0065':rotateAngle,"HorizontalNum":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']*(994236^994232)+(226042^226040)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"VerticalNum":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D']*(744328^744332),'\u006F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065':that['\u006F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065'],'\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']};if(bleTool['\u0074\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){console['\u006C\u006F\u0067']("tcejbo".split("").reverse().join(""),object);imageDataUtils['\u0067\u0065\u0074\u0041\u006C\u006C\u0042\u0079\u0074\u0065\u0073'](object);}else{object['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']*(788016^788020);console['\u006C\u006F\u0067']("\u006F\u0062\u006A\u0065\u0063\u0074",object);imageDataUtils['\u0067\u0065\u0074\u0041\u006C\u006C\u0042\u0079\u0074\u0065\u0073'](object);}that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']=imageDataUtils['\u0067\u0065\u0074\u0057\u0069\u0064\u0074\u0068']();that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']=imageDataUtils['\u0067\u0065\u0074\u0048\u0065\u0069\u0067\u0068\u0074']();printingControl['\u0043\u006F\u006C\u0075\u006D\u006E\u004C\u0065\u0066\u0074']=that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},async initLZMAData(){try{const that=this;let _0x2146e;let bufferTransferCount=200091^200091;_0x2146e=(611423^611417)+(457354^457355);let num=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0043\u006F\u0070\u0069\u0065\u0073'];var _0x37e=(574227^574224)+(188292^188301);let _bufLength=391490^387394;_0x37e=304843^304842;let _0xdcad;let countBuff=new Array();_0xdcad=(849167^849165)+(277075^277072);let _0x2ff34a;let isEndFlag=!![];_0x2ff34a='\u006E\u0064\u0069\u0064\u0067\u006D';let _0x674ef;let imgTotalCount=519271^519270;_0x674ef=478766^478763;let marginleft=imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070']();console['\u006C\u006F\u0067']("\u56FE\u7247\u5DE6\u8FB9\u8DDD",marginleft);let _nColumnTotalCnt=that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-marginleft;console['\u006C\u006F\u0067']("\u6570\u5217\u7247\u56FE".split("").reverse().join(""),_nColumnTotalCnt);var _0xde25dg=(412241^412246)+(562210^562214);let nBytePerLine=Math['\u0066\u006C\u006F\u006F\u0072']((that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(777906^777909))/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);_0xde25dg=404147^404144;console['\u006C\u006F\u0067']("\u6BCF\u5217\u5B57\u8282\u6570",nBytePerLine);let nMax=Math['\u0066\u006C\u006F\u006F\u0072']((_bufLength-(942917^942931))/nBytePerLine);console['\u006C\u006F\u0067']("\u6BCF\u4E2A\u7F13\u51B2\u533A\u6700\u5927\u5217\u6570",nMax);let bufferCountImage=Math['\u0066\u006C\u006F\u006F\u0072']((_nColumnTotalCnt+nMax-(521457^521456))/nMax);console['\u006C\u006F\u0067']("egamItnuoCreffub \u91CF\u6570\u533A\u51B2\u7F13\u7247\u56FE\u4E2A\u6BCF".split("").reverse().join(""),bufferCountImage);let allBytes=imageDataUtils['\u0067\u0065\u0074\u0042\u0079\u0074\u0065\u0073\u0041\u006C\u006C']();console['\u006C\u006F\u0067']("\u0061\u006C\u006C\u0062\u0079\u0074\u0065\u0073\u957F\u5EA6",allBytes['\u006C\u0065\u006E\u0067\u0074\u0068']);for(var n=207017^207017;n<imgTotalCount;n++){that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']++;for(var i=671138^671138;i<bufferCountImage;i++){let _btBuf=new Uint8Array(_bufLength);pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();pr['\u0073\u0065\u0074\u004E\u006F\u0064\u0075'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u006E\u0073\u0069\u0074\u0079']);pr['\u0073\u0065\u0074\u004D\u0061\u0074'](700567^700566);if(i==(481822^481822)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0053\u0074'](918696^918697);}var _0xa_0x316=(627197^627192)+(230218^230210);let bufferColumnCnt=750084^750084;_0xa_0x316=648238^648232;if(i==bufferCountImage-(192969^192968)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0045\u006E\u0064'](661448^661449);if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']==bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()&&isEndFlag){pr['\u0073\u0065\u0074\u0050\u0072\u0074\u0045\u006E\u0064'](963766^963767);}bufferColumnCnt=_nColumnTotalCnt-nMax*i;}else{bufferColumnCnt=nMax;}let star=nMax*i*nBytePerLine+marginleft*nBytePerLine;var _0xd5d63c=(819013^819015)+(155546^155547);let end=star+bufferColumnCnt*nBytePerLine;_0xd5d63c=147470^147463;let b=allBytes['\u0073\u006C\u0069\u0063\u0065'](star,end);_btBuf[557522^557526]=bufferColumnCnt&(253974^254185);_btBuf[859210^859215]=bufferColumnCnt>>(919506^919514)&(278143^278144);for(var y=156819^156819;y<b['\u006C\u0065\u006E\u0067\u0074\u0068'];y++){_btBuf[(975428^975434)+y]=b[y];}let btdata=pr['\u0074\u006F\u0042\u0079\u0074\u0065\u0041\u0072\u0072\u0061\u0079'](344427^344429);for(var z=206355^206353;z<(435411^435415);z++){_btBuf[z]=btdata[z-(534515^534513)];}_btBuf[963797^963795]=nBytePerLine&(860518^860569);if(marginleft>(136005^136005)){_btBuf[513992^513984]=marginleft&(441376^441567);_btBuf[989294^989287]=marginleft>>(147410^147418)&(311063^311272);}else{_btBuf[881560^881552]=155697^155696;_btBuf[408377^408368]=846625^846625;}_btBuf[146055^146061]=(474350^474351)&(936760^936903);_btBuf[585058^585065]=295724^295724;_btBuf[726762^726758]=625474^625474;_btBuf[639950^639939]=472320^472320;let len=_btBuf[749361^749364];len<<=618387^618395;len+=_btBuf[257747^257751];len*=_btBuf[199665^199671];len+=209455^209441;let un=978281^978281;for(var j=161969^161971;j<(338652^338642);j++){un+=_btBuf[j];}var _0x0d6e8g=(742658^742658)+(795704^795706);let x=Math['\u0066\u006C\u006F\u006F\u0072'](len/(175501^175245));_0x0d6e8g=(897014^897013)+(217417^217408);if(x>(502988^502988)){for(var k=577316^577316;k<x;k++){un+=_btBuf[(k+(339082^339083))*(615007^615263)-(748974^748975)];}}_btBuf[976447^976447]=un;_btBuf[150037^150036]=un>>(682101^682109);countBuff['\u0070\u0075\u0073\u0068'](_btBuf);console['\u006C\u006F\u0067']("\u0070\u0072",pr);}}while(bufferTransferCount<bufferCountImage*num&&countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']>(738486^738486)){let _0x6542cc;let sendData=null;_0x6542cc="ikbkog".split("").reverse().join("");let _0x7ee2e;let bufferCount=Math['\u006D\u0069\u006E'](bufferCountImage,countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']);_0x7ee2e=627432^627438;let bufferCountOne=Math['\u006D\u0069\u006E'](bufferCountImage,countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']);do{let bufferPackage=Array();for(let a=653491^653491;a<bufferCount;a++){for(var b=514740^514740;b<_bufLength;b++){bufferPackage[b+a*_bufLength]=countBuff[a][b];}}try{this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']=[];this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']=bufferPackage;sendData=await lzma['\u0067\u0065\u0074\u004C\u007A\u006D\u0061\u0054\u0035\u0030\u0050\u0072\u006F'](bufferPackage);}catch(error){console['\u006C\u006F\u0067']("\u538B\u7F29\u5931\u8D25");sendData=[];that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']=[];num=910272^910272;throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0034'],error);}bufferCount--;}while(sendData['\u004C\u0065\u006E\u0067\u0074\u0068']>_bufLength);bufferTransferCount=bufferTransferCount+bufferCount+(302839^302838);countBuff['\u0073\u0070\u006C\u0069\u0063\u0065'](707347^707347,bufferCountOne);if(sendData['\u006C\u0065\u006E\u0067\u0074\u0068']>(191384^191384)){await that['\u006F\u006E\u0044\u0061\u0074\u0061\u0050\u0072\u006F\u0063\u0065\u0073\u0073\u0069\u006E\u0067'](sendData);}}}catch(error){throw error;}},async onDataProcessing(data){try{const that=this;let _0x8a4c;let sendData=data;_0x8a4c=(730685^730687)+(740968^740961);let type=false;for(var i=826805^826804;i<(415212^415208);i++){let dataIndex3=sendData[i*(843902^844030)+(423893^423894)];let dataIndex2=sendData[i*(522531^522659)+(299789^299791)];let dlen=dataIndex3<<(730261^730269)|dataIndex2;dlen=dlen+(800570^800574);var _0x24e87b=(177174^177183)+(992844^992840);let opcode=sendData[i*(613742^613870)+(166099^166101)];_0x24e87b=(509191^509189)+(495706^495708);let dataIndex6=sendData[i*(711939^712067)+(918388^918385)];if(dlen>(434304^434176)&&(opcode==(883670^883565)||opcode==(489048^489054)||opcode==(254610^254549)||opcode==(636425^636430))&&dataIndex6==(950746^950640)){this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074'][i*(375388^375516)]=this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074'][i*(154904^155032)]+(179507^179506);type=!![];console['\u006C\u006F\u0067']("\u8FDB\u5165\u9519\u8BEF\u003A\u0020","\u91CD\u65B0\u538B\u7F29");}}if(type){sendData=[];console['\u006C\u006F\u0067']("\u518D\u6B21\u538B\u7F29");try{sendData=await lzma['\u0067\u0065\u0074\u004C\u007A\u006D\u0061\u0054\u0035\u0030\u0050\u0072\u006F'](this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']);}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0034'],error);}}that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']['\u0070\u0075\u0073\u0068'](sendData);that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](sendData);}catch(error){throw error;}},doPrint(){const that=this;if(!that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']){that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=!![];if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']!=(450282^450282)){supVanPrintUtils['\u0064\u006F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C'],that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']);}else{this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0035'],"\u6253\u5370\u5B57\u6A21\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A"));}}else{supVanPrintUtils['\u0064\u006F\u0050\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u0050\u0061\u0067\u0065'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C'][332684^332684]);}},cleanImageDataListAll(){this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']=448279^448279;this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=false;},printCallback(callback,type){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B']=callback;this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=type;}};