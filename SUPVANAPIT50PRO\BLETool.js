import supPrintUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import supPrintUtilsMP50 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030\u002E\u006A\u0073";import supPrintUtilsG15 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035\u002E\u006A\u0073";import supPrintUtilsG21 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0032\u0031\u002E\u006A\u0073";import constants from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";import canvasDataRGBAUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0063\u0061\u006E\u0076\u0061\u0073\u0044\u0061\u0074\u0061\u0052\u0047\u0042\u0041\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import lpapi from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u004C\u0050\u0041\u0050\u0049\u002F\u004C\u0050\u0041\u0050\u0049\u002E\u006A\u0073";export default{'\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041':0x11,'\u0043\u004D\u0044\u005F\u0052\u0045\u0041\u0044\u005F\u0044\u0050\u0049':0x22,"SUPPORT_SERVICE_UUID":["7EEF".split("").reverse().join(""),"\u0045\u0030\u0046\u0046"],'\u0053\u0045\u0052\u0056\u0049\u0043\u0045\u005F\u0055\u0055\u0049\u0044':"\u0045\u0030\u0046\u0046",'\u004E\u004F\u0054\u0049\u0046\u0059\u005F\u0055\u0055\u0049\u0044':"\u0046\u0046\u0045\u0031",'\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044':"FFE9","deviceId":'','\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064':'','\u006E\u006F\u0074\u0069\u0066\u0079\u0049\u0064':'','\u0077\u0072\u0069\u0074\u0065\u0049\u0064':'','\u0073\u0065\u0061\u0072\u0063\u0068\u0042\u006C\u0075\u0065\u004C\u0069\u0073\u0074':[],'\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065':false,'\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E':'',"dpiValue":8,wxPromise(api,options={}){return new Promise((resolve,reject)=>{api({...options,"success":res=>resolve(res),"fail":e=>reject(e)});});},async scanBleDeviceList(callBack){var _0x80162a;const that=this;_0x80162a=948849^948851;that['\u0073\u0065\u0061\u0072\u0063\u0068\u0042\u006C\u0075\u0065\u004C\u0069\u0073\u0074']=[];let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0031'];try{await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u006F\u0070\u0065\u006E\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0041\u0064\u0061\u0070\u0074\u0065\u0072']);resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0032'];const stateRes=await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0067\u0065\u0074\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0041\u0064\u0061\u0070\u0074\u0065\u0072\u0053\u0074\u0061\u0074\u0065']);resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0033'];if(!stateRes&&!stateRes['\u0061\u0076\u0061\u0069\u006C\u0061\u0062\u006C\u0065']){resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0035'];return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,"\u84DD\u7259\u9002\u914D\u5668\u4E0D\u53EF\u7528");}await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0073\u0074\u0061\u0072\u0074\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0044\u0065\u0076\u0069\u0063\u0065\u0073\u0044\u0069\u0073\u0063\u006F\u0076\u0065\u0072\u0079'],{'\u0061\u006C\u006C\u006F\u0077\u0044\u0075\u0070\u006C\u0069\u0063\u0061\u0074\u0065\u0073\u004B\u0065\u0079':!![]});resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0034'];wx['\u006F\u006E\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0044\u0065\u0076\u0069\u0063\u0065\u0046\u006F\u0075\u006E\u0064'](res=>{if(res&&res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][687767^687767]&&res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][463301^463301]['\u006E\u0061\u006D\u0065']&&that['\u0061\u006C\u006C\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][615105^615105]['\u006E\u0061\u006D\u0065'])){if(!that['\u0073\u0065\u0061\u0072\u0063\u0068\u0042\u006C\u0075\u0065\u004C\u0069\u0073\u0074']['\u0069\u006E\u0063\u006C\u0075\u0064\u0065\u0073'](res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][567219^567219]['\u006E\u0061\u006D\u0065'])){that['\u0073\u0065\u0061\u0072\u0063\u0068\u0042\u006C\u0075\u0065\u004C\u0069\u0073\u0074']['\u0070\u0075\u0073\u0068'](res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][228868^228868]['\u006E\u0061\u006D\u0065']);callBack(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073'](res));}}});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},async stopScanBleDevices(){try{await this['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0073\u0074\u006F\u0070\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0044\u0065\u0076\u0069\u0063\u0065\u0073\u0044\u0069\u0073\u0063\u006F\u0076\u0065\u0072\u0079']);return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':"\u505C\u6B62\u641C\u7D22\u84DD\u7259\u8BBE\u5907\u6210\u529F"});}catch(error){let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0037'];throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},async disconnectBleDevice(){var _0xc6ec=(256751^256746)+(446449^446451);const that=this;_0xc6ec=602668^602667;try{if(that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E']){if(that['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){await lpapi['\u0063\u006C\u006F\u0073\u0065\u0050\u0072\u0069\u006E\u0074\u0065\u0072']();return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':'断开蓝牙设备成功'});}else{await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u006C\u006F\u0073\u0065\u0042\u004C\u0045\u0043\u006F\u006E\u006E\u0065\u0063\u0074\u0069\u006F\u006E'],{'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':that['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']});return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':"\u65AD\u5F00\u84DD\u7259\u8BBE\u5907\u6210\u529F"});}}else{var _0x_0x190;let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0037'];_0x_0x190=(109609^109600)+(701955^701953);return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,"\u84DD\u7259\u8BBE\u5907\u53F7\u4E3A\u7A7A");}}catch(error){console['\u006C\u006F\u0067'](error);let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0036'];throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},async connectBleDevice(nBleDeviceInfo){try{const that=this;var _0xb617b=(647234^647233)+(818368^818376);let bleDeviceInfo=nBleDeviceInfo;_0xb617b="gjibja".split("").reverse().join("");if(bleDeviceInfo){that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E']=bleDeviceInfo['\u006E\u0061\u006D\u0065'];if(that['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){if(bleDeviceInfo['\u006E\u0061\u006D\u0065']){await this['\u0063\u006F\u006E\u006E\u0065\u0063\u0074\u0044\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065'](bleDeviceInfo['\u006E\u0061\u006D\u0065']);return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':"\u8FDE\u63A5\u5FB7\u4F5F\u84DD\u7259\u8BBE\u5907\u6210\u529F"});}}else{if(bleDeviceInfo['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']){await this['\u0063\u006F\u006E\u006E\u0065\u0063\u0074\u0053\u0075\u0070\u0076\u0061\u006E\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065'](bleDeviceInfo['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']);return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':"\u8FDE\u63A5\u7855\u65B9\u84DD\u7259\u8BBE\u5907\u6210\u529F","serviceUuid":this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064']});}}}}catch(error){throw error;}},async connectDtBleDevice(nBluetoothName){var _0xdafd3d;let bluetoothName=nBluetoothName;_0xdafd3d=784354^784356;return new Promise((resolve,reject)=>{lpapi['\u006F\u0070\u0065\u006E\u0050\u0072\u0069\u006E\u0074\u0065\u0072'](bluetoothName,res=>{resolve(res);},error=>{reject(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0039'],error));});});},async connectSupvanBleDevice(nDeviceId){var _0xa3955g=(619161^619160)+(473013^473014);let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0030'];_0xa3955g="fbmhij".split("").reverse().join("");try{const that=this;var _0x2g8d3a;let deviceId=nDeviceId;_0x2g8d3a="kcnogo".split("").reverse().join("");that['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']=deviceId;that['\u0073\u0065\u0074\u0050\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C'](wx['\u0067\u0065\u0074\u0053\u0079\u0073\u0074\u0065\u006D\u0049\u006E\u0066\u006F\u0053\u0079\u006E\u0063']()['\u006D\u006F\u0064\u0065\u006C']);await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u0072\u0065\u0061\u0074\u0065\u0042\u004C\u0045\u0043\u006F\u006E\u006E\u0065\u0063\u0074\u0069\u006F\u006E'],{'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']});resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0031'];that['\u0062\u006C\u0065\u004D\u0074\u0075']();await that['\u0073\u0074\u006F\u0070\u0053\u0063\u0061\u006E\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073']();resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0032'];var _0x7c29a=(360778^360783)+(764246^764242);const bleDeviceServicesRes=await that['\u0067\u0065\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u0065\u0072\u0076\u0069\u0063\u0065\u0073']();_0x7c29a=(107241^107233)+(392448^392455);resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0033'];bleDeviceServicesRes['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0073']['\u006D\u0061\u0070'](i=>{for(const s of this['\u0053\u0055\u0050\u0050\u004F\u0052\u0054\u005F\u0053\u0045\u0052\u0056\u0049\u0043\u0045\u005F\u0055\u0055\u0049\u0044']){if(i&&i['\u0075\u0075\u0069\u0064']&&i['\u0075\u0075\u0069\u0064']['\u006C\u0065\u006E\u0067\u0074\u0068']>=(817944^817936)){if(i['\u0075\u0075\u0069\u0064']['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']()['\u0074\u006F\u0055\u0070\u0070\u0065\u0072\u0043\u0061\u0073\u0065']()['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](186959^186955,237038^237030)==s){this['\u0075\u0070\u0064\u0061\u0074\u0061\u0042\u006C\u0065\u0055\u0075\u0069\u0064'](s);this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];}}}});const bleDeviceCharacteristicsRes=await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0067\u0065\u0074\u0042\u004C\u0045\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0073'],{"deviceId":this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],'\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064']});resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0034'];bleDeviceCharacteristicsRes['\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0073']['\u006D\u0061\u0070'](i=>{if(i&&i['\u0075\u0075\u0069\u0064']&&i['\u0075\u0075\u0069\u0064']['\u006C\u0065\u006E\u0067\u0074\u0068']>=(565492^565500)){if(i['\u0075\u0075\u0069\u0064']['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']()['\u0074\u006F\u0055\u0070\u0070\u0065\u0072\u0043\u0061\u0073\u0065']()['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](843296^843300,694856^694848)==this['\u004E\u004F\u0054\u0049\u0046\u0059\u005F\u0055\u0055\u0049\u0044']){this['\u006E\u006F\u0074\u0069\u0066\u0079\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];this['\u0077\u0072\u0069\u0074\u0065\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];}else if(i['\u0075\u0075\u0069\u0064']['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']()['\u0074\u006F\u0055\u0070\u0070\u0065\u0072\u0043\u0061\u0073\u0065']()['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](336801^336805,218234^218226)==this['\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044']){this['\u0077\u0072\u0069\u0074\u0065\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];}}});await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u006E\u006F\u0074\u0069\u0066\u0079\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065\u0043\u0068\u0061\u006E\u0067\u0065'],{'\u0073\u0074\u0061\u0074\u0065':!![],"deviceId":this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],'\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064'],'\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0049\u0064':this['\u006E\u006F\u0074\u0069\u0066\u0079\u0049\u0064']});resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0035'];wx['\u006F\u006E\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065\u0043\u0068\u0061\u006E\u0067\u0065'](res=>{if(that['\u0074\u0035\u0030\u0050\u0072\u006F\u0041\u006E\u0064\u0054\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtils['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}else if(that['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtilsMP50['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}else if(that['\u0067\u0053\u0065\u0072\u0069\u0065\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtilsG15['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}else if(that['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtilsG21['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},updataBleUuid(currentServiceUuid){switch(currentServiceUuid){case"\u0046\u0045\u0045\u0037":this['\u0053\u0045\u0052\u0056\u0049\u0043\u0045\u005F\u0055\u0055\u0049\u0044']="\u0046\u0045\u0045\u0037";this['\u004E\u004F\u0054\u0049\u0046\u0059\u005F\u0055\u0055\u0049\u0044']="1CEF".split("").reverse().join("");this['\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044']="\u0046\u0045\u0043\u0031";break;case"FF0E".split("").reverse().join(""):this['\u0053\u0045\u0052\u0056\u0049\u0043\u0045\u005F\u0055\u0055\u0049\u0044']="\u0045\u0030\u0046\u0046";this['\u004E\u004F\u0054\u0049\u0046\u0059\u005F\u0055\u0055\u0049\u0044']="1EFF".split("").reverse().join("");this['\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044']="\u0046\u0046\u0045\u0039";break;default:break;}},async stopPrint(callback){try{var _0x2_0xb9g;const that=this;_0x2_0xb9g='\u0063\u006E\u006C\u0067\u006C\u0063';if(this['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0044\u0074'](!![]);}else{canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0044\u0074'](false);supPrintUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);supPrintUtilsMP50['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtilsMP50['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);supPrintUtilsG15['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtilsG15['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);supPrintUtilsG21['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtilsG21['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);}}catch(error){throw error;}},async bleMtu(){if(wx['\u0067\u0065\u0074\u0053\u0079\u0073\u0074\u0065\u006D\u0049\u006E\u0066\u006F\u0053\u0079\u006E\u0063']()['\u0070\u006C\u0061\u0074\u0066\u006F\u0072\u006D']=="diordna".split("").reverse().join("")){await new Promise(resolve=>setTimeout(resolve,458008^458480));return this['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0073\u0065\u0074\u0042\u004C\u0045\u004D\u0054\u0055'],{'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],"mtu":182});}return Promise['\u0072\u0065\u0073\u006F\u006C\u0076\u0065'];},async getBleDeviceServices(){await new Promise(resolve=>setTimeout(resolve,405995^406583));return this['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0067\u0065\u0074\u0042\u004C\u0045\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u0065\u0072\u0076\u0069\u0063\u0065\u0073'],{'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']});},onWriteBLECharacteristicValue(nbyteData){let byteData=nbyteData;console['\u006C\u006F\u0067'](":\u636E\u6570\u9001\u53D1".split("").reverse().join(""),byteData);if(byteData){const buffer=new ArrayBuffer(byteData['\u006C\u0065\u006E\u0067\u0074\u0068']);var _0xc15dfd=(900393^900384)+(347972^347980);const dataView=new DataView(buffer);_0xc15dfd=886871^886879;for(var i=896775^896775;i<byteData['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){dataView['\u0073\u0065\u0074\u0055\u0069\u006E\u0074\u0038'](i,byteData[i]);}wx['\u0077\u0072\u0069\u0074\u0065\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065']({'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],'\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064'],'\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0049\u0064':this['\u0077\u0072\u0069\u0074\u0065\u0049\u0064'],'\u0076\u0061\u006C\u0075\u0065':dataView['\u0062\u0075\u0066\u0066\u0065\u0072'],"success":res=>{},'\u0066\u0061\u0069\u006C':res=>{console['\u006C\u006F\u0067']("\u0077\u0072\u0069\u0074\u0065\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065\u0020\u0066\u0061\u0069\u006C",res);}});}},t50ProBleDevices(nBluetoothName){const bluetoothName=nBluetoothName;const validPrefixes=["\u0054\u0030\u0031\u0030\u0030\u0041","A9900T".split("").reverse().join(""),"\u0054\u0030\u0030\u0034\u0036\u0041","\u0054\u0030\u0030\u0031\u0033\u0042","B0200T".split("").reverse().join(""),"B4200T".split("").reverse().join(""),"\u0054\u0030\u0030\u0039\u0037\u0041","A1010T".split("").reverse().join(""),"\u0054\u0030\u0031\u0031\u0034\u0041","\u0054\u0030\u0031\u0031\u0035\u0041","A2110T".split("").reverse().join(""),"A3110T".split("").reverse().join(""),"\u0054\u0030\u0031\u0034\u0035\u0042","B6410T".split("").reverse().join(""),"\u0054\u0030\u0031\u0034\u0039\u0042","B0510T".split("").reverse().join(""),"\u0054\u0030\u0031\u0035\u0031\u0042","\u0054\u0030\u0031\u0035\u0032\u0042","\u0054\u0030\u0031\u0035\u0033\u0042","B4510T".split("").reverse().join(""),"B5510T".split("").reverse().join(""),"\u0054\u0030\u0031\u0035\u0036\u0042","B3010T".split("").reverse().join(""),"\u0054\u0030\u0030\u0032\u0031\u0042","\u0054\u0030\u0031\u0035\u0039\u0042","\u0054\u0030\u0031\u0036\u0030\u0042","B1610T".split("").reverse().join(""),"\u0054\u0030\u0031\u0036\u0037\u0042","B8610T".split("").reverse().join(""),"\u0054\u0030\u0031\u0036\u0039\u0042","\u0054\u0030\u0031\u0036\u0032\u0042","\u0054\u0030\u0031\u0036\u0033\u0042","\u0054\u0030\u0031\u0038\u0035\u0042"];if(bluetoothName){return validPrefixes['\u0073\u006F\u006D\u0065'](prefix=>bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068'](prefix));}return false;},t80BleDevices(nBluetoothName){var _0xd7b95e=(649266^649269)+(787223^787223);let bluetoothName=nBluetoothName;_0xd7b95e=(159584^159585)+(491258^491250);if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A3300T".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0031\u0031\u0031\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A9010T".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0031\u0037\u0035\u0041")){return!![];}else{return false;}}},t50ProAndT80BleDevices(nBluetoothName){var _0x8737e=(346209^346210)+(779282^779285);let bluetoothName=nBluetoothName;_0x8737e=259203^259200;if(this['\u0074\u0035\u0030\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0074\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}},mp50BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A3000PM".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0050\u0030\u0030\u0030\u0034\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A5000PM".split("").reverse().join(""))){return!![];}else{return false;}}},dtBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("05T".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0038\u0030")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("05PM".split("").reverse().join(""))){return!![];}else{return false;}}},g15BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0033\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0034")){return!![];}else{return false;}},e11BleDevices(nBluetoothName){var _0xf0ba4e=(205467^205465)+(223104^223109);let bluetoothName=nBluetoothName;_0xf0ba4e=988516^988512;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A8310T".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0031\u0033\u0039\u0041")){return!![];}else{return false;}},g11BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A2200G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A1400G".split("").reverse().join(""))){return!![];}else{return false;}},g11ProBleDevices(nBluetoothName){var _0xba8f=(191718^191715)+(951425^951429);let bluetoothName=nBluetoothName;_0xba8f=(507316^507315)+(633180^633182);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A8200G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0038\u0042")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0035\u0041")){return!![];}else{return false;}},g11MBleDevices(nBluetoothName){var _0x1c9be=(669851^669843)+(477188^477189);let bluetoothName=nBluetoothName;_0x1c9be=(806377^806378)+(273107^273107);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A3200G".split("").reverse().join(""))){return!![];}else{return false;}},g11MPlusBleDevices(nBluetoothName){var _0x243c6e;let bluetoothName=nBluetoothName;_0x243c6e=(576337^576344)+(777819^777820);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A5200G".split("").reverse().join(""))){return!![];}else{return false;}},g15MBleDevices(nBluetoothName){var _0xfe971b=(814310^814311)+(592641^592645);let bluetoothName=nBluetoothName;_0xfe971b=594314^594319;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("4000G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0037\u0041")){return!![];}else{return false;}},g15MProBleDevices(nBluetoothName){var _0x3e5g3b;let bluetoothName=nBluetoothName;_0x3e5g3b=475765^475762;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0038\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("B8100G".split("").reverse().join(""))){return!![];}else{return false;}},g15MiniBleDevices(nBluetoothName){var _0x956d=(498455^498463)+(980586^980586);let bluetoothName=nBluetoothName;_0x956d=(569866^569867)+(934489^934493);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0030\u0033\u0041")){return!![];}else{return false;}},g15ProBleDevices(nBluetoothName){var _0x91451e;let bluetoothName=nBluetoothName;_0x91451e="kbnpep".split("").reverse().join("");if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A5100G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("6100G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0035\u0042")){return!![];}else{return false;}},g18ProBleDevices(nBluetoothName){var _0xa4a9fg;let bluetoothName=nBluetoothName;_0xa4a9fg=(290757^290757)+(791832^791824);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0032\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0032\u0042")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("4400G".split("").reverse().join(""))){return!![];}else{return false;}},g19MPlusBleDevices(nBluetoothName){var _0x80c8fb=(746105^746110)+(983059^983063);let bluetoothName=nBluetoothName;_0x80c8fb="jcfjfm".split("").reverse().join("");if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0037\u0041")){return!![];}else{return false;}},g19PlusBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0036\u0041")){return!![];}else{return false;}},g28BleDevices(nBluetoothName){var _0x5g_0x2b1=(494034^494036)+(310987^310978);let bluetoothName=nBluetoothName;_0x5g_0x2b1=843812^843815;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0033\u0041")){return!![];}else{return false;}},q11PlusBleDevices(nBluetoothName){var _0xf6f=(942079^942076)+(277054^277050);let bluetoothName=nBluetoothName;_0xf6f=(619415^619410)+(591449^591441);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A1100D".split("").reverse().join(""))){return!![];}else{return false;}},q11ProBleDevices(nBluetoothName){var _0xgd877e=(585284^585284)+(490759^490759);let bluetoothName=nBluetoothName;_0xgd877e=682792^682798;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0031\u0033\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0031\u0033\u0042")){return!![];}else{return false;}},q15BleDevices(nBluetoothName){var _0x79ebg=(740118^740127)+(915529^915529);let bluetoothName=nBluetoothName;_0x79ebg=(916348^916340)+(237707^237698);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0030\u0038")){return!![];}else{return false;}},q15MiniBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0030\u0034\u0041")){return!![];}else{return false;}},q15ProBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A9000D".split("").reverse().join(""))){return!![];}else{return false;}},q18BleDevices(nBluetoothName){var _0xf1ebd;let bluetoothName=nBluetoothName;_0xf1ebd=550295^550289;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A0100D".split("").reverse().join(""))){return!![];}else{return false;}},q19PlusBleDevices(nBluetoothName){var _0x666fc=(399587^399584)+(979005^979000);let bluetoothName=nBluetoothName;_0x666fc=(432328^432334)+(990791^990784);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A2100D".split("").reverse().join(""))){return!![];}else{return false;}},g21BleDevices(nBluetoothName){var _0x4bd81e=(124097^124100)+(479012^479013);let bluetoothName=nBluetoothName;_0x4bd81e=945292^945291;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A4200G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0039")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0031\u0034")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0030")){return!![];}else{return false;}},gSeriesBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(this['\u0067\u0031\u0035\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0065\u0031\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u004D\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u004D\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u004D\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u004D\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u004D\u0069\u006E\u0069\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0038\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0039\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0039\u004D\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0031\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0031\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0035\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0035\u004D\u0069\u006E\u0069\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0035\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0039\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}},allBleDevices(nBluetoothName){var _0xc762g=(392455^392463)+(536432^536432);let bluetoothName=nBluetoothName;_0xc762g='\u0064\u0065\u0064\u0068\u006F\u0069';if(bluetoothName){if(this['\u0074\u0035\u0030\u0050\u0072\u006F\u0041\u006E\u0064\u0054\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0074\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0053\u0065\u0072\u0069\u0065\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}}},g21OrG28BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName){if(this['\u0067\u0032\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}}},setPhoneModel(nPhoneModel){var _0xbe2ea=(673330^673328)+(845863^845856);let phoneModel=nPhoneModel;_0xbe2ea=(616759^616758)+(431787^431786);if(phoneModel){if(phoneModel['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0049\u0020\u0043\u0043\u0020\u0039\u0065")||phoneModel['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0050\u0044\u0056\u004D\u0030\u0030")||phoneModel['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0050\u0042\u0041\u004D\u0030\u0030")){this['\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065']=!![];}else{this['\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065']=false;}}},getPhoneModel(){return this['\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065'];},getFDpiValue(){return this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},setFDpiValue(nValue){var _0xgca;const that=this;_0xgca=(585246^585245)+(434185^434176);let value=nValue;var _0x0b67c=(248011^248015)+(369637^369637);let bluetoothName=that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'];_0x0b67c=249877^249878;if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0050\u0030\u0030\u0030\u0034\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0050\u0030\u0030\u0030\u0035\u0041")){this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=770559^770547;}else if(this['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=value;}else{this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=575342^575334;}}},getMaxDotValueType(nBluetoothName){var _0x791be=(588810^588811)+(407108^407107);let bluetoothName=nBluetoothName;_0x791be=(613473^613472)+(952602^952594);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0050\u0030\u0030\u0030\u0034\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0050\u0030\u0030\u0030\u0035\u0041")){return!![];}else{return false;}}};