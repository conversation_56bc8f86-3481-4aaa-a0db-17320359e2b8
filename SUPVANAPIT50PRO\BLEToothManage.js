import supPrintUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import canvasDataRGBAUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0063\u0061\u006E\u0076\u0061\u0073\u0044\u0061\u0074\u0061\u0052\u0047\u0042\u0041\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import barCodeUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import imageEncodeUtilsT50Pro from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0054\u0035\u0030\u0050\u0072\u006F\u002E\u006A\u0073";import imageEncodeUtilsMp50 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030\u002E\u006A\u0073";import drawQrcode from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0077\u0065\u0061\u0070\u0070\u002E\u0071\u0072\u0063\u006F\u0064\u0065\u002E\u0065\u0073\u006D\u002E\u006A\u0073";import constants from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";import bleTool from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C\u002E\u006A\u0073";import SupVanPrintUtilsMP50 from"\u002E\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030\u002E\u006A\u0073";import SupVanPrintUtilsG15 from"\u002E\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035\u002E\u006A\u0073";import imageEncodeUtilsG15 from"\u002E\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035\u002E\u006A\u0073";import SupVanPrintUtilsG21 from"\u002E\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0032\u0031\u002E\u006A\u0073";import imageEncodeUtilsG21 from"\u002E\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0047\u0032\u0031\u002E\u006A\u0073";export default{'\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E':'','\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061':{'\u0077\u0069\u0064\u0074\u0068':'',"height":'','\u0070\u0061\u0074\u0068':''},'\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068':'',"dataMeasure":{'\u0077\u0069\u0064\u0074\u0068':'','\u0068\u0065\u0069\u0067\u0068\u0074':'',"barcodeWidth":'','\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074':'',"qrcodeWidth":'','\u0071\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074':''},"qrCodeObject":{},'\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C':[],'\u0069\u006D\u0061\u0067\u0065\u004C\u0069\u0073\u0074\u0041\u006C\u006C':[],"myCanvasRGBA":null,'\u0063\u0061\u006E\u0076\u0061\u0073\u0042\u0061\u0072\u0043\u006F\u0064\u0065':null,'\u0069\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D':0,'\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068':400,"templateHeight":302,"objectAll":null,"connectCallBack":null,'\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065':constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u004D\u0041\u0054\u0052\u0049\u0058'],'\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065':8,async doPrintMatrix(myCanvasRGBA,nObjectData,canvasBarCode,callback){try{var _0x1ec5a;const that=this;_0x1ec5a=(355148^355148)+(211628^211629);let objectData=nObjectData;if(!myCanvasRGBA){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0038'],"\u6587\u672C\u0043\u0061\u006E\u0076\u0061\u0073\u4E0D\u80FD\u4E3A\u7A7A");}if(constants['\u0069\u0073\u0041\u0072\u0072\u0061\u0079\u0045\u006D\u0070\u0074\u0079'](objectData)){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0036'],"\u6A21\u677F\u5BF9\u8C61\u4E0D\u80FD\u4E3A\u7A7A");}bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](662492^662484);if(bleTool['\u0065\u0031\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](objectData[436083^436083]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](await that['\u0071\u0075\u0065\u0072\u0079\u0047\u0031\u0035\u0044\u0070\u0069']());}else if(bleTool['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](objectData[570139^570139]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](await that['\u0072\u0065\u0061\u0064\u0047\u0032\u0031\u0044\u0070\u0069'](objectData[137568^137568]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']));}that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();console['\u006C\u006F\u0067']("eulavipd".split("").reverse().join(""),that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);that['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback,constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u004D\u0041\u0054\u0052\u0049\u0058']);that['\u006D\u0061\u0074\u0072\u0069\u0078\u0043\u006F\u0070\u0069\u0065\u0073'](myCanvasRGBA,objectData,canvasBarCode);const objectList=await that['\u0061\u006E\u0061\u006C\u0079\u0073\u0069\u0073\u0054\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0044\u0061\u0074\u0061']();callback(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073'](that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065'],constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0030']));await that['\u006D\u0061\u0074\u0072\u0069\u0078\u0043\u0061\u006E\u0076\u0061\u0073'](objectList);}catch(error){throw error;}},async queryG15Dpi(){return new Promise(resolve=>{SupVanPrintUtilsG15['\u0071\u0075\u0065\u0072\u0079\u0044\u0050\u0049'](result=>{let dpi=result;let dpiValue=546580^546588;if(dpi>=(553305^553957)&&dpi<=(101412^102148)){dpiValue=dpi/(802699^802799);}resolve(dpiValue);});});},async readG21Dpi(deviceSn){return new Promise(resolve=>{SupVanPrintUtilsG21['\u0072\u0065\u0061\u0064\u0044\u0050\u0049'](result=>{console['\u006C\u006F\u0067']("\u0072\u0065\u0073\u0075\u006C\u0074",result);var _0xfg11d;let dpi=result;_0xfg11d='\u006D\u0065\u0065\u0071\u0066\u006A';var _0x7d0edb=(888199^888206)+(338003^338001);let dpiValue=dpi/(416839^416803);_0x7d0edb='\u006F\u006B\u0064\u0069\u0065\u006B';var _0xfe732a=(125542^125542)+(876486^876482);let G28Device=bleTool['\u0067\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](deviceSn);_0xfe732a="ohgekd".split("").reverse().join("");if(G28Device){if(dpi<(180143^179171)||dpi>(626198^625318)){dpiValue=418350^418338;}}else{if(dpi<(116059^116711)||dpi>(441582^442318)){dpiValue=465624^465616;}}resolve(Math['\u0063\u0065\u0069\u006C'](dpiValue));});});},async printNextMatrix(){try{var _0xabg;const that=this;_0xabg="dlqbch".split("").reverse().join("");var _0xc7a;const objectList=await that['\u0061\u006E\u0061\u006C\u0079\u0073\u0069\u0073\u0054\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0044\u0061\u0074\u0061']();_0xc7a=(622830^622831)+(324480^324485);await that['\u006D\u0061\u0074\u0072\u0069\u0078\u0043\u0061\u006E\u0076\u0061\u0073'](objectList);}catch(error){throw error;}},async matrixCanvas(objectList){try{var _0xd5d46e=(709540^709536)+(676198^676195);const that=this;_0xd5d46e=(860223^860222)+(781561^781552);var _0xac4cd=(777319^777314)+(944483^944484);const objectData=await that['\u0071\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061'](objectList);_0xac4cd=(329907^329910)+(390807^390805);await canvasDataRGBAUtils['\u006D\u0061\u0074\u0072\u0069\u0078\u0044\u0061\u0074\u0061\u0043\u0061\u006E\u0076\u0061\u0073'](objectData);}catch(error){throw error;}},matrixCopies(myCanvasRGBA,objectData,canvasBarCode){const that=this;that['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u004D\u0041\u0054\u0052\u0049\u0058'];that['\u0063\u006C\u0065\u0061\u006E\u0044\u0061\u0074\u0061']();canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0044\u0074'](false);that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']=myCanvasRGBA;that['\u0063\u0061\u006E\u0076\u0061\u0073\u0042\u0061\u0072\u0043\u006F\u0064\u0065']=canvasBarCode;that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];var _0xe3d=(911288^911295)+(276616^276609);let objectListAllFirst=objectData['\u0073\u006C\u0069\u0063\u0065']();_0xe3d="hqjmlg".split("").reverse().join("");for(let object of objectListAllFirst){if(!object['\u0043\u006F\u0070\u0069\u0065\u0073']||object['\u0043\u006F\u0070\u0069\u0065\u0073']==(402305^402305)){object['\u0043\u006F\u0070\u0069\u0065\u0073']=626750^626751;}for(let i=681448^681448;i<object['\u0043\u006F\u0070\u0069\u0065\u0073'];i++){that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](object);}}that['\u0073\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D'](objectData);},async doPrintImage(myCanvasRGBA,pageImageListData,callback){try{const that=this;if(!myCanvasRGBA){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0038'],"\u7A7A\u4E3A\u80FD\u4E0DsavnaC\u672C\u6587".split("").reverse().join(""));}if(constants['\u0069\u0073\u0041\u0072\u0072\u0061\u0079\u0045\u006D\u0070\u0074\u0079'](pageImageListData)){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0036'],"\u6A21\u677F\u5BF9\u8C61\u4E0D\u80FD\u4E3A\u7A7A");}bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](362088^362080);if(bleTool['\u0065\u0031\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](pageImageListData[941379^941379]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](await that['\u0071\u0075\u0065\u0072\u0079\u0047\u0031\u0035\u0044\u0070\u0069']());}else if(bleTool['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](pageImageListData[762817^762817]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](await that['\u0072\u0065\u0061\u0064\u0047\u0032\u0031\u0044\u0070\u0069'](pageImageListData[253141^253141]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']));}that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();console['\u006C\u006F\u0067']("eulavipd".split("").reverse().join(""),that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);that['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback,constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u0049\u004D\u0041\u0047\u0045']);that['\u0069\u006D\u0061\u0067\u0065\u0043\u006F\u0070\u0069\u0065\u0073'](myCanvasRGBA,pageImageListData);var _0xdaee=(329459^329463)+(915850^915849);const objectData=that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061']();_0xdaee='\u0066\u0070\u006A\u006B\u0063\u006C';callback(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073'](that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065'],constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0030']));await canvasDataRGBAUtils['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u0043\u0061\u006E\u0076\u0061\u0073'](objectData);}catch(error){throw error;}},async printNextImage(){try{const that=this;const objectData=that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061']();await canvasDataRGBAUtils['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u0043\u0061\u006E\u0076\u0061\u0073'](objectData);}catch(error){throw error;}},imageCopies(myCanvasRGBA,pageImageListData){const that=this;that['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u0049\u004D\u0041\u0047\u0045'];that['\u0063\u006C\u0065\u0061\u006E\u0044\u0061\u0074\u0061']();canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0044\u0074'](false);that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']=myCanvasRGBA;that['\u0069\u006D\u0061\u0067\u0065\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];var _0x3a318a;let imageListAllFirst=pageImageListData['\u0073\u006C\u0069\u0063\u0065']();_0x3a318a=(150951^150951)+(269386^269386);for(let object of imageListAllFirst){if(!object['\u0043\u006F\u0070\u0069\u0065\u0073']||object['\u0043\u006F\u0070\u0069\u0065\u0073']==(352098^352098)){object['\u0043\u006F\u0070\u0069\u0065\u0073']=100300^100301;}for(let i=357774^357774;i<object['\u0043\u006F\u0070\u0069\u0065\u0073'];i++){that['\u0069\u006D\u0061\u0067\u0065\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](object);}}that['\u0073\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D'](pageImageListData);},async doDrawPreview(myCanvasRGBA,nObjectData,canvasBarCode,callback){try{var _0x194b1c;const that=this;_0x194b1c=(262546^262550)+(519357^519357);let objectData=nObjectData;bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](965700^965708);that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();if(!myCanvasRGBA){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0038'],"\u6587\u672C\u0043\u0061\u006E\u0076\u0061\u0073\u4E0D\u80FD\u4E3A\u7A7A");}if(constants['\u0069\u0073\u0041\u0072\u0072\u0061\u0079\u0045\u006D\u0070\u0074\u0079'](objectData)){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0036'],"\u6A21\u677F\u5BF9\u8C61\u4E0D\u80FD\u4E3A\u7A7A");}canvasDataRGBAUtils['\u0064\u0072\u0061\u0077\u0050\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);await that['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u006F\u0070\u0069\u0065\u0073'](myCanvasRGBA,objectData,canvasBarCode);var _0x48572a=(999005^999003)+(173295^173287);const objectList=await that['\u0061\u006E\u0061\u006C\u0079\u0073\u0069\u0073\u0054\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0044\u0061\u0074\u0061']();_0x48572a=621899^621890;callback(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073'](that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065'],constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0030']));await that['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006E\u0076\u0061\u0073'](objectList);}catch(error){throw error;}},async drawNextPreview(){try{var _0x34gfc;const that=this;_0x34gfc=(559614^559608)+(843369^843368);var _0xce542b=(911422^911418)+(966433^966440);const objectList=await that['\u0061\u006E\u0061\u006C\u0079\u0073\u0069\u0073\u0054\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0044\u0061\u0074\u0061']();_0xce542b=998093^998095;await that['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006E\u0076\u0061\u0073'](objectList);}catch(error){throw error;}},async previewCanvas(objectList){try{var _0x531e=(707630^707628)+(763254^763262);const that=this;_0x531e=(995324^995323)+(524756^524755);var _0xd56f=(789778^789779)+(885235^885237);const objectData=await that['\u0071\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061'](objectList);_0xd56f=(671315^671317)+(873743^873735);canvasDataRGBAUtils['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0044\u0061\u0074\u0061\u0043\u0061\u006E\u0076\u0061\u0073'](objectData);}catch(error){throw error;}},async previewCopies(myCanvasRGBA,objectData,canvasBarCode){var _0xed11e;const that=this;_0xed11e='\u0071\u0063\u006F\u0062\u006C\u0071';that['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u0050\u0052\u0045\u0056\u0049\u0045\u0057'];that['\u0063\u006C\u0065\u0061\u006E\u0044\u0061\u0074\u0061']();that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']=myCanvasRGBA;that['\u0063\u0061\u006E\u0076\u0061\u0073\u0042\u0061\u0072\u0043\u006F\u0064\u0065']=canvasBarCode;that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];let objectListAllFirst=objectData['\u0073\u006C\u0069\u0063\u0065']();for(let object of objectListAllFirst){if(!object['\u0043\u006F\u0070\u0069\u0065\u0073']||object['\u0043\u006F\u0070\u0069\u0065\u0073']==(414168^414168)){object['\u0043\u006F\u0070\u0069\u0065\u0073']=344935^344934;}for(let i=650801^650801;i<object['\u0043\u006F\u0070\u0069\u0065\u0073'];i++){that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](object);}}that['\u0073\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D'](objectData);},async analysisTemplateData(){try{var _0xde1ace=(181018^181010)+(887373^887370);const that=this;_0xde1ace='\u0063\u006B\u006D\u0068\u0064\u0066';let barcodeObject={};that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']={};that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']='';var _0x6277ad=(335608^335612)+(250701^250693);let objectList=that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0073\u0068\u0069\u0066\u0074']();_0x6277ad=(389019^389023)+(286072^286072);that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068']=objectList['\u0057\u0069\u0064\u0074\u0068'];that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0048\u0065\u0069\u0067\u0068\u0074']=objectList['\u0048\u0065\u0069\u0067\u0068\u0074'];let drawObjects=objectList['\u0044\u0072\u0061\u0077\u004F\u0062\u006A\u0065\u0063\u0074\u0073'];for(let i in drawObjects){if(drawObjects[i]['\u0046\u006F\u0072\u006D\u0061\u0074']=="EDOCRAB".split("").reverse().join("")){barcodeObject=drawObjects[i];}else if(drawObjects[i]['\u0046\u006F\u0072\u006D\u0061\u0074']=="\u0051\u0052\u0043\u004F\u0044\u0045"){that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']=drawObjects[i];}else if(drawObjects[i]['\u0046\u006F\u0072\u006D\u0061\u0074']=="\u0049\u004D\u0041\u0047\u0045"){that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']=drawObjects[i]['\u0043\u006F\u006E\u0074\u0065\u006E\u0074'];}}if(that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']&&Object['\u006B\u0065\u0079\u0073'](that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074'])['\u006C\u0065\u006E\u0067\u0074\u0068']>(517876^517876)){that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0071\u0072\u0063\u006F\u0064\u0065\u0057\u0069\u0064\u0074\u0068']=that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0071\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074']=that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];}if(barcodeObject&&Object['\u006B\u0065\u0079\u0073'](barcodeObject)['\u006C\u0065\u006E\u0067\u0074\u0068']>(437271^437271)&&that['\u0063\u0061\u006E\u0076\u0061\u0073\u0042\u0061\u0072\u0043\u006F\u0064\u0065']){const barCodeRes=await barCodeUtils['\u0067\u0065\u0074\u0042\u0061\u0072\u0063\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061'](barcodeObject,that['\u0063\u0061\u006E\u0076\u0061\u0073\u0042\u0061\u0072\u0043\u006F\u0064\u0065']);that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0057\u0069\u0064\u0074\u0068']=barCodeRes['\u0077\u0069\u0064\u0074\u0068'];that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074']=barCodeRes['\u0068\u0065\u0069\u0067\u0068\u0074'];let canvasId=barCodeRes['\u0063\u0061\u006E\u0076\u0061\u0073'];const filePathRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'](canvasId);that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0070\u0061\u0074\u0068']=filePathRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'];that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0077\u0069\u0064\u0074\u0068']=barCodeRes['\u0077\u0069\u0064\u0074\u0068'];that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0068\u0065\u0069\u0067\u0068\u0074']=barCodeRes['\u0068\u0065\u0069\u0067\u0068\u0074'];}if(bleTool['\u0067\u0053\u0065\u0072\u0069\u0065\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](objectList['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])||bleTool['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](objectList['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){objectList['\u004D\u0061\u0072\u0067\u0069\u006E']=425543^425541;if(objectList&&objectList['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']&&objectList['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(759548^759551)){let maxWidth=objectList['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];for(const textData of objectList['\u0044\u0072\u0061\u0077\u004F\u0062\u006A\u0065\u0063\u0074\u0073']){if(textData['\u0046\u006F\u0072\u006D\u0061\u0074']="TXET".split("").reverse().join("")){that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0073\u0065\u0074\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065'](textData['\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);var _0x38f9g;const metrics=that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u006D\u0065\u0061\u0073\u0075\u0072\u0065\u0054\u0065\u0078\u0074'](textData['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']);_0x38f9g='\u0063\u006C\u0065\u0062\u006A\u0065';const currentWidth=metrics['\u0077\u0069\u0064\u0074\u0068'];if(currentWidth>maxWidth){maxWidth=currentWidth;}textData['\u0057\u0069\u0064\u0074\u0068']=Math['\u0063\u0065\u0069\u006C'](maxWidth/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(520894^520893));}}objectList['\u0057\u0069\u0064\u0074\u0068']=Math['\u0063\u0065\u0069\u006C'](maxWidth/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(840786^840785));that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068']=Math['\u0063\u0065\u0069\u006C'](maxWidth/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(146080^146083));}}that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0077\u0069\u0064\u0074\u0068']=that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0068\u0065\u0069\u0067\u0068\u0074']=that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];return objectList;}catch(error){throw error;}},imageData(){var _0x3da4b=(258202^258203)+(117133^117132);const that=this;_0x3da4b=(383441^383446)+(793937^793938);var _0xdd61b=(198358^198366)+(503020^503012);let imageListAll=that['\u0069\u006D\u0061\u0067\u0065\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0073\u0068\u0069\u0066\u0074']();_0xdd61b="dicbmn".split("").reverse().join("");that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068']=imageListAll['\u0057\u0069\u0064\u0074\u0068'];that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0048\u0065\u0069\u0067\u0068\u0074']=imageListAll['\u0048\u0065\u0069\u0067\u0068\u0074'];that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0077\u0069\u0064\u0074\u0068']=that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0068\u0065\u0069\u0067\u0068\u0074']=that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];that['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C']=imageListAll;that['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C']['\u004D\u0061\u0072\u0067\u0069\u006E']=965071^965069;return{'\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041':that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041'],'\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C'],'\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D':that['\u0069\u006D\u0061\u0067\u0065\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']};},async qrCodeData(objectList){var _0x85b62f;const that=this;_0x85b62f=(514918^514919)+(969991^969998);that['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C']=objectList;var _0xb732ca;let qrFilePath='';_0xb732ca=981942^981938;if(that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']&&Object['\u006B\u0065\u0079\u0073'](that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074'])['\u006C\u0065\u006E\u0067\u0074\u0068']>(173805^173805)&&that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']){qrFilePath=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0051\u0072\u0043\u006F\u0064\u0065']();}return{'\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041':that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041'],"objectAll":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C'],'\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061':that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061'],'\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D':that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068'],"qrFilePath":qrFilePath,'\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068':that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068'],"printType":that['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']};},async canvasQrCode(){try{const that=this;await new Promise(resolve=>setTimeout(resolve,372301^372351));drawQrcode({'\u0077\u0069\u0064\u0074\u0068':that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"height":that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0063\u0061\u006E\u0076\u0061\u0073\u0049\u0064':"\u0071\u0072\u0043\u006F\u0064\u0065","text":that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']});var _0x6cb;const qrfilePathRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068\u0051\u0072\u0043\u006F\u0064\u0065']();_0x6cb='\u006A\u0067\u006B\u0062\u006A\u006D';let qrFilePath=qrfilePathRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'];console['\u006C\u006F\u0067']("\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068",that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']);if(that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']){const downLoadFileRes=await that['\u0064\u006F\u0077\u006E\u006C\u006F\u0061\u0064\u0046\u0069\u006C\u0065'](that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']);that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']=downLoadFileRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'];}return qrFilePath;}catch(error){throw error;}},downloadFile(url){try{return constants['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0064\u006F\u0077\u006E\u006C\u006F\u0061\u0064\u0046\u0069\u006C\u0065'],{"url":url});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0031'],error);}},canvasToTempFilePathQrCode(){try{return constants['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'],{"canvasId":"\u0071\u0072\u0043\u006F\u0064\u0065","fileType":'png','\u0071\u0075\u0061\u006C\u0069\u0074\u0079':1.0});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0030'],error);}},canvasToTempFilePath(canvasId){return new Promise((resolve,reject)=>{wx['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']({"canvas":canvasId,'\u0066\u0069\u006C\u0065\u0054\u0079\u0070\u0065':"\u0070\u006E\u0067",'\u0071\u0075\u0061\u006C\u0069\u0074\u0079':1.0,"success":res=>{console['\u006C\u006F\u0067']("\u529F\u6210\u7247\u56FE\u6210\u751F".split("").reverse().join(""),res);resolve(res);},"fail":error=>{console['\u006C\u006F\u0067']("\u751F\u6210\u56FE\u7247\u5931\u8D25",error);reject(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0039'],error));}},this);});},getImageAllNum(){return this['\u0069\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D'];},setImageAllNum(nObjectData){var _0xdb_0xa97=(337705^337711)+(552818^552827);let objectData=nObjectData;_0xdb_0xa97=461716^461717;this['\u0069\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']=461984^461984;for(let data of objectData){this['\u0069\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']=Number(this['\u0069\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D'])+Number(data['\u0043\u006F\u0070\u0069\u0065\u0073']);}},cleanBarcodeData(){this['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0077\u0069\u0064\u0074\u0068']='';this['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0068\u0065\u0069\u0067\u0068\u0074']='';this['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0070\u0061\u0074\u0068']='';},cleanDataMeasure(){this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0077\u0069\u0064\u0074\u0068']='';this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0068\u0065\u0069\u0067\u0068\u0074']='';this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0057\u0069\u0064\u0074\u0068']='';this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074']='';this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0071\u0072\u0063\u006F\u0064\u0065\u0057\u0069\u0064\u0074\u0068']='';this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0071\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074']='';},cleanData(){var _0x35a09e;const that=this;_0x35a09e='\u006F\u006D\u006B\u0071\u0068\u0069';canvasDataRGBAUtils['\u0063\u006C\u0065\u0061\u006E\u0049\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074']();imageEncodeUtilsT50Pro['\u0063\u006C\u0065\u0061\u006E\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']();imageEncodeUtilsMp50['\u0063\u006C\u0065\u0061\u006E\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']();imageEncodeUtilsG15['\u0063\u006C\u0065\u0061\u006E\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']();imageEncodeUtilsG21['\u0063\u006C\u0065\u0061\u006E\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']();that['\u0063\u006C\u0065\u0061\u006E\u0042\u0061\u0072\u0063\u006F\u0064\u0065\u0044\u0061\u0074\u0061']();that['\u0063\u006C\u0065\u0061\u006E\u0044\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']();},printCallback(nCallBack,nType){let callBack=nCallBack;var _0xed1b2c=(381744^381748)+(709079^709076);let type=nType;_0xed1b2c=829165^829163;supPrintUtils['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);SupVanPrintUtilsMP50['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);SupVanPrintUtilsG15['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);SupVanPrintUtilsG21['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);imageEncodeUtilsT50Pro['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);imageEncodeUtilsMp50['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);imageEncodeUtilsG15['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);imageEncodeUtilsG21['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);canvasDataRGBAUtils['\u0064\u0074\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);}};