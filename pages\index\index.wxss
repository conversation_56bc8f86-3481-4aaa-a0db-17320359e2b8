/**index.wxss**/
page {
  background-color: #F4F9FD;
}
.connect-blue {
  display: flex;
  justify-content: center;
  background-color: antiquewhite;
  margin: 20rpx 0;

}

.connect-blue text {
  font-size: 31rpx;
  font-weight: 500;
  color: #000000;
  padding: 20rpx 0rpx;
}

.list-wrap {
  width: 750rpx;
  background: #F7F8FA;
  margin-bottom: 23rpx;
}

.shop-name {
  display: flex;
  margin: 10rpx;
  padding: 30rpx;
  background-color: antiquewhite;
}

.text-shop-name {
  font-size: 23rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #999999;
}

.text-shop-name-content {
  font-size: 25rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
  margin-top: 10rpx;
}

.printnum-text {
  display: flex;
  justify-content: center;
  background-color: antiquewhite;
  margin: 20rpx 0;

}
.input-text {
  display: flex;

  align-items: center;
  height: 50rpx;
}

.preview-image-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.preview-image {
  width: 80%;
  max-height: 80%;
  background-color: white;
  border-radius: 8px;
}

.close-preview {
  margin-top: 20px;
  padding: 8px 16px;
  background-color: #007AFF;
  color: white;
  border-radius: 4px;
}

