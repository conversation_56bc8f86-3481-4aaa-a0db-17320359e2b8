<!--index.wxml-->
<view style="background-color:#F4F9FD">
  <view style="position: fixed;top: 9999px;">
    <!-- 绘制内容 -->
    <view>
      <canvas width='{{templateWidth*pixelRatio}}' height='{{templateHeight*pixelRatio}}' style="width:{{templateWidth}}px;height:{{templateHeight}}px" canvas-id="Canvas" id="Canvas"></canvas>
    </view>
    <!-- 绘制条形码 -->
    <view>
      <canvas type="2d" style="width:{{barCodeWidth}}px;height:{{barCodeHeight}}px" class="ls-barcode-canvas" canvas-id="barCodels" id="barCodels"></canvas>
    </view>
    <!-- 绘制二维码 -->
    <view>
      <canvas style="width:{{qrCodeWidth}}px; height: {{qrCodeHeight}}px;" canvas-id="qrCode"></canvas>
    </view>
  </view>

  <view class="connect-blue">
    <text bindtap="clickDisconnectBleDevice">点击断开蓝牙</text>
  </view>
  <view class="connect-blue">
    <text bindtap="clickStartScanBleDevice">点击开始蓝牙搜索</text>
  </view>
  <view class="connect-blue">
    <text bindtap="clickStopScanBleDevices">点击停止蓝牙搜索</text>
  </view>

  <view class="connect-blue">
    <text bindtap="clickStartPrintTemplatePageObjectLocal">点击开始打印示例模板</text>
  </view>
  <view class="connect-blue">
    <text bindtap="clickStartDrawPreview">点击开始绘制预览图</text>
  </view>
  <!-- 预览图显示区域 -->
  <view class="preview-image-container" wx:if="{{showPreview}}">
    <image src="{{previewImagePath}}" mode="widthFix" class="preview-image"></image>
    <view class="close-preview" bindtap="closePreview">关闭预览</view>
  </view>
  <view class="connect-blue">
    <text bindtap="clickStartPrintTemplatePageImageLocal">点击开始打印图片</text>
  </view>
  <view class="connect-blue">
    <text bindtap="clickStopPrint">点击终止打印</text>
  </view>
  <view class="printnum-text">
    <text>打印份数：</text>
    <text>{{printNum}}</text>
  </view>
  <view class="input-text">
    <input type="number" placeholder="输入打印份数" bindinput="startEnterPrintCopies" />
  </view>
  <view style="height: 80vh;overflow:auto;">
    <view class="list-wrap" wx:for="{{blueList}}" wx:key="id" wx:for-item="item">
      <view class="shop-name" data-index="{{index}}" bindtap="clickConnectBleDevice">
        <text class="text-shop-name">蓝牙名称</text>
        <text class="text-shop-name">{{item.name}}</text>
      </view>
    </view>
  </view>
</view>
