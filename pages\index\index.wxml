<!--index.wxml-->
<view style="background-color:#F4F9FD">
  <view style="position: fixed;top: 9999px;">
    <!-- 绘制内容 -->
    <view>
      <canvas width='{{templateWidth*pixelRatio}}' height='{{templateHeight*pixelRatio}}' style="width:{{templateWidth}}px;height:{{templateHeight}}px" canvas-id="Canvas" id="Canvas"></canvas>
    </view>
    <!-- 绘制条形码 -->
    <view>
      <canvas type="2d" style="width:{{barCodeWidth}}px;height:{{barCodeHeight}}px" class="ls-barcode-canvas" canvas-id="barCodels" id="barCodels"></canvas>
    </view>
    <!-- 绘制二维码 -->
    <view>
      <canvas style="width:{{qrCodeWidth}}px; height: {{qrCodeHeight}}px;" canvas-id="qrCode"></canvas>
    </view>
  </view>

  <view class="connect-blue">
    <text bindtap="clickDisconnectBleDevice">点击断开蓝牙</text>
  </view>
  <view class="connect-blue">
    <text bindtap="clickStartScanBleDevice">点击开始蓝牙搜索</text>
  </view>
  <view class="connect-blue">
    <text bindtap="clickStopScanBleDevices">点击停止蓝牙搜索</text>
  </view>

  <view class="connect-blue">
    <text bindtap="clickStartPrintTemplatePageObjectLocal">点击开始打印示例模板</text>
  </view>

  <view class="connect-blue">
    <text bindtap="clickStartDrawPreview">点击开始绘制预览图</text>
  </view>
  <!-- 预览图显示区域 -->
  <view class="preview-image-container" wx:if="{{showPreview}}">
    <image src="{{previewImagePath}}" mode="widthFix" class="preview-image"></image>
    <view class="close-preview" bindtap="closePreview">关闭预览</view>
  </view>

  <view class="connect-blue">
    <text bindtap="openEditTemplate">编辑标签内容</text>
  </view>
  <!-- 编辑模版弹窗 -->
  <view class="edit-template-modal" wx:if="{{showEditTemplate}}">
    <view class="edit-template-content">
      <view class="edit-template-header">
        <text class="edit-template-title">编辑模版内容</text>
        <view class="close-edit-template" bindtap="closeEditTemplate">×</view>
      </view>

      <!-- 预览图区域 -->
      <view class="edit-preview-container">
        <image wx:if="{{editPreviewImagePath}}" src="{{editPreviewImagePath}}" mode="widthFix" class="edit-preview-image"></image>
        <view wx:else class="edit-preview-placeholder">预览图生成中...</view>
      </view>

      <!-- 编辑表单 -->
      <view class="edit-form">
        <view class="edit-form-item">
          <text class="edit-label">品名：</text>
          <input class="edit-input" type="text" value="{{editableContent.productName}}" bindinput="onProductNameInput" placeholder="请输入品名" />
        </view>
        <view class="edit-form-item">
          <text class="edit-label">操作人：</text>
          <input class="edit-input" type="text" value="{{editableContent.operator}}" bindinput="onOperatorInput" placeholder="请输入操作人" />
        </view>
        <view class="edit-form-item">
          <text class="edit-label">日期：</text>
          <input class="edit-input" type="text" value="{{editableContent.date}}" bindinput="onDateInput" placeholder="请输入日期" />
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="edit-actions">
        <view class="edit-btn edit-btn-cancel" bindtap="closeEditTemplate">取消</view>
        <view class="edit-btn edit-btn-print" bindtap="printEditedTemplate">打印</view>
      </view>
    </view>
  </view>
  <view class="connect-blue">
    <text bindtap="clickStartPrintTemplatePageImageLocal">点击开始打印图片</text>
  </view>
  <view class="connect-blue">
    <text bindtap="clickStopPrint">点击终止打印</text>
  </view>
  <view class="printnum-text">
    <text>打印份数：</text>
    <text>{{printNum}}</text>
  </view>
  <view class="input-text">
    <input type="number" placeholder="输入打印份数" bindinput="startEnterPrintCopies" />
  </view>
  <view style="height: 80vh;overflow:auto;">
    <view class="list-wrap" wx:for="{{blueList}}" wx:key="id" wx:for-item="item">
      <view class="shop-name" data-index="{{index}}" bindtap="clickConnectBleDevice">
        <text class="text-shop-name">蓝牙名称</text>
        <text class="text-shop-name">{{item.name}}</text>
      </view>
    </view>
  </view>
</view>
