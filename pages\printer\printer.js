// printer.js
import bleTool from '../../SUPVANAPIT50PRO/BLETool.js'
import bleToothManage from '../../SUPVANAPIT50PRO/BLEToothManage.js'
import constants from '../../SUPVANAPIT50PRO/Constants.js'

Page({
  data: {
    // 打印机状态
    printerStatus: 'disconnected', // disconnected, connecting, error, connected, printing
    printerDeviceSn: '',
    printerErrorCode: null, // 错误状态码
    printerErrorMessage: '', // 错误信息
    isPrinting: false, // 是否正在打印
    isConnecting: false, // 是否正在连接
    
    // 模版相关
    templates: [], // 模版列表
    selectedTemplateIndex: 0, // 当前选中的模版索引
    previewImagePath: '', // 预览图路径
    
    // 标签内容
    labelContent: {
      productName: '品名',
      operator: '操作人',
      date: '',
      copies: 1
    },
    
    // 蓝牙相关
    blueList: [],
    isScanning: false,
    showDeviceSelector: false,
    
    // 联系我们弹窗
    showContactModal: false,
    
    // Canvas相关
    templateWidth: 560,
    templateHeight: 302,
    barCodeWidth: 214,
    barCodeHeight: 72,
    qrCodeWidth: 20,
    qrCodeHeight: 20,
    pixelRatio: 2,
    canvasBarCode: null,
    canvasText: null,

    // 耗材信息相关
    materialInfo: null, // 当前耗材信息
    materialMismatch: false, // 耗材规格是否不匹配
    forcePrint: false // 是否允许强制打印
  },

  // 状态码映射表
  statusCodeMap: {
    0: '请求成功',
    1: '请求成功',
    100: '模板信息回调',
    101: '初始化蓝牙模块异常',
    102: '获取本机蓝牙适配器状态异常',
    103: '开始搜寻附近的蓝牙外围设备异常',
    104: '蓝牙寻找到新设备的事件异常',
    105: '蓝牙适配器不可用',
    106: '断开蓝牙失败异常',
    107: '蓝牙序列号为空',
    108: '文本Canvas不能为空',
    109: '连接T50,T80蓝牙异常',
    110: '连接硕方蓝牙异常',
    111: 'blemtu异常',
    112: '获取蓝牙设备服务异常',
    113: '获取蓝牙特征值异常',
    114: '获取特征值变化异常',
    115: '获取notify异常',
    116: '模板对象不能为空',
    117: '停止搜索蓝牙设备成功',
    118: '获取条形码对象数据异常',
    119: '生成图片失败异常',
    120: '二维码转换成图片异常',
    121: '图片下载异常',
    122: '获取rgba字模数据异常',
    123: '下载本地图片异常',
    124: '生成图片数据异常',
    125: 'T50,T80机器启动打印异常',
    126: '请关闭耗材仓盖',
    127: '耗材未装好',
    128: '请检查耗材余量',
    129: '未检测到耗材',
    130: '未识别到耗材',
    131: '耗材已用完',
    132: '打印异常终止',
    133: '色带错误',
    134: '压缩失败',
    135: '打印字模数据不能为空'
  },

  onLoad() {
    this.initPage()
  },


  onReady() {
    // 尝试连接上次使用的打印机
    // this.tryConnectLastPrinter()
  },

  onUnload(){
    this.disconnectDevice()
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 设置当前日期
    const today = new Date()
    const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`

    // 加载上次保存的标签内容
    const savedContent = wx.getStorageSync('lastLabelContent')
    const labelContent = savedContent ? {
      ...savedContent,
      date: /*savedContent.date ||*/ dateStr // 如果没有保存日期，使用当前日期
    } : {
      productName: '品名',
      operator: '操作人',
      date: dateStr,
      copies: 1
    }

    this.setData({
      labelContent: labelContent,
      pixelRatio: wx.getWindowInfo().pixelRatio,
      canvasText: wx.createCanvasContext('Canvas', this),
      canvasBarCode: wx.createSelectorQuery().in(this)
    })

    // 加载模版配置
    this.loadTemplates()

    // 尝试连接上次使用的打印机
    // this.tryConnectLastPrinter()
  },

  /**
   * 加载模版配置
   */
  loadTemplates() {
    // 从本地存储获取上次选中的模版索引
    const lastSelectedIndex = wx.getStorageSync('lastSelectedTemplateIndex') || 0

    // 尝试从网络加载模版配置文件，如果失败则使用默认配置
    this.loadTemplatesFromNetwork().then(templates => {
      this.setData({
        templates: templates,
        selectedTemplateIndex: lastSelectedIndex < templates.length ? lastSelectedIndex : 0
      })
      this.generatePreview()
    }).catch(() => {
      // 网络加载失败，使用默认配置
      const templates = [
      {
        "TemplateName": "40 * 30 mm",
        "Width": 40,
        "Height": 30,
        "Rotate": 0,
        "Copies": 1,
        "Density": 2,
        "HorizontalNum": 0,
        "VerticalNum": 0,
        "PaperType": 1,
        "Gap": 8,
        "Speed": 25,
        "FirstCut": 1,
        "CutType": 1,
        "DeviceSn": "T0145B",
        "ImageWidth": 40,
        "ImageHeight": 30,
        "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/blank.png",
        "DrawObjects": [
          {
            "AntiColor": false,
            "X": 2,
            "Y": 6,
            "Width": 35,
            "Height": 4,
            "Content": "品名：_____________",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "4",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 14,
            "Y": 5,
            "Width": 28,
            "Height": 4,
            "Content": "品名",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "5",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 2,
            "Y": 14,
            "Width": 35,
            "Height": 4,
            "Content": "操作员：____________",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "4",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 17,
            "Y": 13,
            "Width": 20,
            "Height": 4,
            "Content": "操作人",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "5",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 2,
            "Y": 22,
            "Width": 35,
            "Height": 4,
            "Content": "日期：_____________",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "4",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 14,
            "Y": 21,
            "Width": 23,
            "Height": 4,
            "Content": "2025-06-17",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "5",
            "Format": "TEXT",
            "Orientation": 0
          }
        ]
      },
      {
        "TemplateName": "50 * 28 mm",
        "Width": 50,
        "Height": 28,
        "Rotate": 0,
        "Copies": 1,
        "Density": 2,
        "HorizontalNum": 0,
        "VerticalNum": 0,
        "PaperType": 1,
        "Gap": 3,
        "Speed": 25,
        "FirstCut": 1,
        "CutType": 1,
        "DeviceSn": "T0145B",
        "ImageWidth": 50,
        "ImageHeight": 28,
        "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/blank.png",
        "DrawObjects": [
          {
            "AntiColor": false,
            "X": 2,
            "Y": 5,
            "Width": 45,
            "Height": 4,
            "Content": "品名：___________________",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "4",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 14,
            "Y": 4,
            "Width": 33,
            "Height": 4,
            "Content": "品名",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "5",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 2,
            "Y": 13,
            "Width": 45,
            "Height": 4,
            "Content": "操作员：__________________",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "4",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 17,
            "Y": 12,
            "Width": 30,
            "Height": 4,
            "Content": "操作人",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "5",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 2,
            "Y": 21,
            "Width": 45,
            "Height": 4,
            "Content": "日期：___________________",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "4",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 14,
            "Y": 20,
            "Width": 33,
            "Height": 4,
            "Content": "2025-06-17",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "5",
            "Format": "TEXT",
            "Orientation": 0
          }
        ]
      }
    ]
    
      this.setData({
        templates: templates,
        selectedTemplateIndex: lastSelectedIndex < templates.length ? lastSelectedIndex : 0
      })
      this.generatePreview()
    })
  },

  /**
   * 从网络加载模版配置
   */
  loadTemplatesFromNetwork() {
    return new Promise((resolve, reject) => {
      // 这里可以从服务器加载配置文件
      // 目前使用本地默认配置
      reject('使用本地配置')
    })
  },

  /**
   * 尝试连接上次使用的打印机
   */
  tryConnectLastPrinter() {
    const lastPrinter = wx.getStorageSync('lastConnectedPrinter')
    if (lastPrinter) {
      this.setData({
        printerDeviceSn: lastPrinter.name || lastPrinter.deviceId
      })
      // 这里可以尝试连接上次的打印机
      // bleTool.connectBleDevice(lastPrinter)...
      this.connectDevice(lastPrinter)
    }
  },

  /**
   * 选择模版
   */
  selectTemplate(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      selectedTemplateIndex: index
    })

    // 保存选择到本地存储
    wx.setStorageSync('lastSelectedTemplateIndex', index)

    // 检查耗材规格匹配
    if (this.data.printerStatus === 'connected') {
      this.checkMaterialCompatibility()
    }

    // 重新生成预览图
    this.generatePreview()
  },

  /**
   * 生成预览图
   */
  generatePreview() {
    const template = this.data.templates[this.data.selectedTemplateIndex]
    if (!template) return

    // 创建临时模版对象，更新内容
    const tempTemplate = JSON.parse(JSON.stringify(template))
    tempTemplate.Copies = parseInt(this.data.labelContent.copies) || 1

    // 更新DrawObjects中的内容（只更新可编辑的内容，保留固定的标题和下划线）
    if (tempTemplate.DrawObjects && tempTemplate.DrawObjects.length >= 6) {
      // 更新品名内容（索引1）
      tempTemplate.DrawObjects[1].Content = this.data.labelContent.productName
      // 更新操作人内容（索引3）
      tempTemplate.DrawObjects[3].Content = this.data.labelContent.operator
      // 更新日期内容（索引5）
      tempTemplate.DrawObjects[5].Content = this.data.labelContent.date
    }

    const that = this
    bleToothManage.doDrawPreview(this.data.canvasText, [tempTemplate], this.data.canvasBarCode, res => {
      if (res.ResultCode == constants.globalResultCode.ResultCode100) {
        let resultValue = res.ResultValue
        this.setData({
          templateWidth: resultValue.width,
          templateHeight: resultValue.height,
          barCodeWidth: resultValue.barcodeWidth,
          barCodeHeight: resultValue.barcodeHeight,
          qrCodeWidth: resultValue.qrcodeWidth,
          qrCodeHeight: resultValue.qrcodeHeight,
        })
      } else if (res.ResultCode == constants.globalResultCode.ResultCodeSuccess) {
        if (res.ResultValue && res.ResultValue.previewList && res.ResultValue.previewList.length > 0) {
          that.setData({
            previewImagePath: res.ResultValue.previewList[0]
          })
        }
      }
    }).catch(error => {
      console.error('生成预览图失败:', error)
    })
  },

  /**
   * 保存标签内容到本地存储
   */
  saveLabelContent() {
    wx.setStorageSync('lastLabelContent', this.data.labelContent)
  },

  /**
   * 获取耗材信息
   */
  getMaterialInfo() {
    const that = this
    return new Promise((resolve, reject) => {
      // 调用获取耗材信息的API
      // 注意：这里使用的是 ConsumableInformation 方法，您可能需要根据实际API调整
      bleToothManage.ConsumableInformation().then(res => {
        console.log('获取耗材信息回调:', res)
        if (res.ResultCode === 0 || res.ResultCode === 1) {
          // 获取成功
          const materialInfo = res.ResultValue
          that.setData({
            materialInfo: materialInfo
          })
          resolve(materialInfo)
        } else {
          // 获取失败
          console.error('获取耗材信息失败:', res)
          reject(res)
        }
      }).catch(error => {
        console.error('获取耗材信息异常:', error)
        reject(error)
      })
    })
  },

  /**
   * 检查耗材规格兼容性
   */
  checkMaterialCompatibility() {
    if (!this.data.materialInfo) {
      // 如果没有耗材信息，先获取
      this.getMaterialInfo().then(() => {
        this.performCompatibilityCheck()
      }).catch(error => {
        console.error('无法获取耗材信息进行兼容性检查:', error)
      })
    } else {
      this.performCompatibilityCheck()
    }
  },

  /**
   * 执行兼容性检查
   */
  performCompatibilityCheck() {
    const template = this.data.templates[this.data.selectedTemplateIndex]
    const materialInfo = this.data.materialInfo

    if (!template || !materialInfo) {
      return
    }

    // 比较耗材规格与模版规格
    // paperDirectionSize 对应 Height
    // printHeadDirectionSize 对应 Width
    // gap 对应 Gap
    const heightMatch = materialInfo.paperDirectionSize === template.Height
    const widthMatch = materialInfo.printHeadDirectionSize === template.Width
    const gapMatch = materialInfo.gap === template.Gap

    const isCompatible = heightMatch && widthMatch && gapMatch

    this.setData({
      materialMismatch: !isCompatible
    })

    if (!isCompatible) {
      // 更新状态栏显示不匹配信息
      this.setData({
        printerErrorMessage: '当前耗材规格与模版不一致',
        printerErrorCode: null
      })

      console.log('耗材规格不匹配:', {
        template: {
          width: template.Width,
          height: template.Height,
          gap: template.Gap
        },
        material: {
          width: materialInfo.printHeadDirectionSize,
          height: materialInfo.paperDirectionSize,
          gap: materialInfo.gap
        },
        match: {
          heightMatch,
          widthMatch,
          gapMatch
        }
      })
    } else {
      // 规格匹配，清除错误信息
      if (this.data.printerErrorMessage === '当前耗材规格与模版不一致') {
        this.setData({
          printerErrorMessage: '',
          printerErrorCode: null
        })
      }
    }
  },

  /**
   * 处理打印机状态码
   */
  handlePrinterStatus(resultCode) {
    const message = this.statusCodeMap[resultCode] || `未知错误(${resultCode})`

    // 成功状态码 - 设备连接正常
    if (resultCode === 0 || resultCode === 1 || resultCode === 100 || resultCode === 117) {
      return {
        status: 'connected',
        message: message,
        isConnected: true
      }
    }

    // 打印相关错误（126-135）- 设备已连接但打印有问题
    if (resultCode >= 126 && resultCode <= 135) {
      return {
        status: 'connected', // 设备仍然连接，只是打印有问题
        message: message,
        isPrintError: true,
        isConnected: true
      }
    }

    // 蓝牙连接相关错误（101-115）- 设备连接问题
    if (resultCode >= 101 && resultCode <= 115) {
      return {
        status: 'error',
        message: message,
        isConnected: false
      }
    }

    // 其他错误 - 根据具体情况判断
    if (resultCode === 116 || resultCode === 118 || resultCode === 119 ||
        resultCode === 120 || resultCode === 121 || resultCode === 122 ||
        resultCode === 123 || resultCode === 124 || resultCode === 135) {
      // 这些错误通常表示设备已连接，但数据处理有问题
      return {
        status: 'connected',
        message: message,
        isConnected: true
      }
    }

    // 默认为连接错误
    return {
      status: 'error',
      message: message,
      isConnected: false
    }
  },

  /**
   * 更新打印机状态
   */
  updatePrinterStatus(resultCode, customMessage = '') {
    const statusInfo = this.handlePrinterStatus(resultCode)

    this.setData({
      printerStatus: statusInfo.status,
      printerErrorCode: resultCode,
      printerErrorMessage: customMessage || statusInfo.message
    })

    // 如果设备仍然连接，保持设备信息
    if (statusInfo.isConnected && this.data.printerDeviceSn) {
      // 保持设备连接信息不变
    } else if (!statusInfo.isConnected) {
      // 设备断开连接，清除设备信息
      this.setData({
        printerDeviceSn: ''
      })
    }
  },

  /**
   * 输入品名
   */
  onProductNameInput(e) {
    this.setData({
      'labelContent.productName': e.detail.value.slice(0, 20) // 限制20个字符
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 输入操作人
   */
  onOperatorInput(e) {
    this.setData({
      'labelContent.operator': e.detail.value.slice(0, 20) // 限制20个字符
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 选择日期
   */
  onDateChange(e) {
    this.setData({
      'labelContent.date': e.detail.value
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 输入打印份数
   */
  onCopiesInput(e) {
    let value = e.detail.value
    // 允许空值，用户可能正在删除内容
    if (value === '') {
      this.setData({
        'labelContent.copies': ''
      })
      return
    }

    let copies = parseInt(value) || 1
    copies = Math.max(1, Math.min(250, copies)) // 限制1-250
    this.setData({
      'labelContent.copies': copies
    })
    this.saveLabelContent() // 保存内容
    // 移除预览图刷新，因为份数与内容无关
  },

  /**
   * 增加打印份数
   */
  increaseCopies() {
    let copies = parseInt(this.data.labelContent.copies) || 1
    copies = Math.min(250, copies + 1) // 最大250
    this.setData({
      'labelContent.copies': copies
    })
    this.saveLabelContent() // 保存内容
    // 移除预览图刷新，因为份数与内容无关
  },

  /**
   * 减少打印份数
   */
  decreaseCopies() {
    let copies = parseInt(this.data.labelContent.copies) || 1
    copies = Math.max(1, copies - 1) // 最小1
    this.setData({
      'labelContent.copies': copies
    })
    this.saveLabelContent() // 保存内容
    // 移除预览图刷新，因为份数与内容无关
  },

  /**
   * 连接打印机/打印标签/停止打印按钮
   */
  onPrintAction() {
    // 防止重复操作
    if (this.data.isConnecting || this.data.isScanning) {
      const message = this.data.isScanning ? '正在搜索设备...' : '正在连接中...'
      wx.showToast({
        title: message,
        icon: 'none'
      })
      return
    }

    if (this.data.isPrinting) {
      // 正在打印，停止打印
      this.stopPrint()
    } else if (this.data.printerStatus === 'connected') {
      // 已连接，执行打印
      this.printLabel()
    } else {
      // 未连接，开始连接流程
      this.startConnectPrinter()
    }
  },

  /**
   * 开始连接打印机流程
   */
  startConnectPrinter() {
    // 检查蓝牙授权
    wx.authorize({
      scope: 'scope.bluetooth',
      success: () => {
        this.scanAndConnectPrinter()
      },
      fail: () => {
        wx.showModal({
          title: '需要蓝牙权限',
          content: '请授权蓝牙功能以连接打印机',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting()
            }
          }
        })
      }
    })
  },

  /**
   * 扫描并连接打印机
   */
  scanAndConnectPrinter() {
    this.setData({
      isScanning: true,
      blueList: []
    })

    const that = this
    let scanTimeout = null

    // 2秒后检查扫描结果
    scanTimeout = setTimeout(() => {
      bleTool.stopScanBleDevices()
      that.setData({ isScanning: false })

      if (that.data.blueList.length === 1) {
        // 只有一台设备，直接连接
        that.connectDevice(that.data.blueList[0])
      } else if (that.data.blueList.length > 1) {
        // 多台设备，显示选择对话框
        that.setData({ showDeviceSelector: true })
      } else {
        // 没有找到设备
        that.setData({
          printerStatus: 'error',
          printerErrorMessage: '未找到可用的打印机',
          printerErrorCode: null
        })
        wx.showToast({
          title: '未找到可用的打印机',
          icon: 'none'
        })
      }
    }, 2000)

    // 开始扫描
    bleTool.scanBleDeviceList((res) => {
      if (res.ResultCode == 0) {
        if (res && res.ResultValue && res.ResultValue.devices[0]) {
          that.data.blueList.push(res.ResultValue.devices[0])
          that.setData({
            blueList: that.data.blueList
          })
        }
      }
    }).catch(error => {
      console.log('搜索蓝牙设备失败:', error)
      that.setData({ isScanning: false })
      clearTimeout(scanTimeout)
    })
  },

  /**
   * 连接指定设备
   */
  connectDevice(device) {
    // 设置连接中状态
    this.setData({
      isConnecting: true,
      printerStatus: 'connecting',
      printerErrorCode: null,
      printerErrorMessage: ''
    })

    const that = this
    bleTool.connectBleDevice(device).then(res => {
      console.log('连接结果:', res)

      // 处理连接结果状态
      const statusInfo = that.handlePrinterStatus(res.ResultCode)

      if (statusInfo.isConnected) {
        // 设备连接成功
        that.setData({
          isConnecting: false,
          printerStatus: statusInfo.status,
          printerDeviceSn: device.name || device.deviceId,
          showDeviceSelector: false,
          printerErrorCode: statusInfo.isPrintError ? res.ResultCode : null,
          printerErrorMessage: statusInfo.isPrintError ? statusInfo.message : ''
        })

        // 保存到本地存储
        wx.setStorageSync('lastConnectedPrinter', device)

        // 更新模版的DeviceSn
        const templates = that.data.templates
        templates.forEach(template => {
          template.DeviceSn = device.name || device.deviceId
        })
        that.setData({ templates })

        // 连接成功后获取耗材信息并检查兼容性
        that.getMaterialInfo().then(() => {
          that.checkMaterialCompatibility()
        }).catch(error => {
          console.log('获取耗材信息失败，但设备已连接:', error)
        })

        if (statusInfo.isPrintError) {
          wx.showToast({
            title: `设备已连接，但${statusInfo.message}`,
            icon: 'none',
            duration: 3000
          })
        } else {
          wx.showToast({
            title: '连接成功',
            icon: 'success'
          })
        }
      } else {
        // 连接失败
        that.setData({
          isConnecting: false,
          printerStatus: 'error',
          printerErrorCode: res.ResultCode,
          printerErrorMessage: statusInfo.message,
          showDeviceSelector: false
        })

        wx.showToast({
          title: statusInfo.message,
          icon: 'none',
          duration: 3000
        })
      }
    }).catch(error => {
      console.log('连接异常:', error)
      that.setData({
        isConnecting: false,
        printerStatus: 'error',
        printerErrorCode: 109,
        printerErrorMessage: '连接蓝牙异常'
      })
      wx.showToast({
        title: '连接蓝牙异常',
        icon: 'error'
      })
    })
  },

  /**
   * 选择设备对话框中的设备选择
   */
  selectDevice(e) {
    const index = e.currentTarget.dataset.index
    const device = this.data.blueList[index]

    // 停止扫描
    if (this.data.isScanning) {
      bleTool.stopScanBleDevices()
      this.setData({
        isScanning: false
      })
    }

    // 检查是否为当前已连接设备
    const currentDeviceId = this.data.printerDeviceSn
    const selectedDeviceId = device.name || device.deviceId

    if (currentDeviceId === selectedDeviceId) {
      // 选择的是当前设备，不需要重新连接
      wx.showToast({
        title: '当前设备已连接',
        icon: 'success'
      })
      this.setData({
        showDeviceSelector: false
      })
      return
    }

    // 连接新设备
    this.connectDevice(device)
  },

  /**
   * 关闭设备选择对话框
   */
  closeDeviceSelector() {
    // 停止扫描
    if (this.data.isScanning) {
      bleTool.stopScanBleDevices()
    }

    this.setData({
      showDeviceSelector: false,
      isScanning: false
    })
  },

  /**
   * 更改设备
   */
  changeDevice() {
    // 防止重复操作
    if (this.data.isScanning || this.data.isConnecting) {
      wx.showToast({
        title: '操作进行中，请稍候',
        icon: 'none'
      })
      return
    }

    // 开始搜索设备并显示选择对话框
    this.setData({
      isScanning: true,
      blueList: [],
      showDeviceSelector: true
    })

    const that = this

    // 开始扫描，持续搜索直到用户手动停止
    bleTool.scanBleDeviceList((res) => {
      if (res.ResultCode == 0) {
        if (res && res.ResultValue && res.ResultValue.devices[0]) {
          const device = res.ResultValue.devices[0]
          // 检查是否已存在
          const exists = that.data.blueList.find(item =>
            (item.deviceId === device.deviceId) || (item.name === device.name)
          )
          if (!exists) {
            that.data.blueList.push(device)
            that.setData({
              blueList: that.data.blueList
            })
          }
        }
      }
    }).catch(error => {
      console.log('搜索蓝牙设备失败:', error)
      that.setData({
        isScanning: false,
        showDeviceSelector: false
      })
      wx.showToast({
        title: '搜索设备失败',
        icon: 'error'
      })
    })
  },

  /**
   * 打印标签
   */
  printLabel() {
    const template = this.data.templates[this.data.selectedTemplateIndex]
    if (!template) {
      this.updatePrinterStatus(116) // 模板对象不能为空
      wx.showToast({
        title: '请选择模版',
        icon: 'none'
      })
      return
    }

    // 检查耗材规格兼容性
    if (this.data.materialMismatch && !this.data.forcePrint) {
      wx.showModal({
        title: '耗材规格不匹配',
        content: '当前耗材规格与模版不一致，是否强制打印？',
        confirmText: '强制打印',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 用户选择强制打印
            this.setData({
              forcePrint: true
            })
            this.executePrint()
          }
        }
      })
      return
    }

    this.executePrint()
  },

  /**
   * 执行打印
   */
  executePrint() {
    // 设置打印状态
    this.setData({
      isPrinting: true,
      printerStatus: 'printing'
    })

    const template = this.data.templates[this.data.selectedTemplateIndex]

    // 创建打印模版对象
    const printTemplate = JSON.parse(JSON.stringify(template))
    printTemplate.Copies = parseInt(this.data.labelContent.copies) || 1

    // 更新DrawObjects中的内容（只更新可编辑的内容，保留固定的标题和下划线）
    if (printTemplate.DrawObjects && printTemplate.DrawObjects.length >= 6) {
      // 更新品名内容（索引1）
      printTemplate.DrawObjects[1].Content = this.data.labelContent.productName
      // 更新操作人内容（索引3）
      printTemplate.DrawObjects[3].Content = this.data.labelContent.operator
      // 更新日期内容（索引5）
      printTemplate.DrawObjects[5].Content = this.data.labelContent.date
    }

    const that = this
    // 执行打印
    bleToothManage.doPrintMatrix(this.data.canvasText, [printTemplate], this.data.canvasBarCode, res => {
      console.log('打印回调:', res)

      // 处理打印状态
      const statusInfo = that.handlePrinterStatus(res.ResultCode)

      // 停止打印状态
      that.setData({
        isPrinting: false
      })

      if (res.ResultCode === 0 || res.ResultCode === 1 || res.ResultCode === 100) {
        // 打印成功，重置强制打印标志
        that.setData({
          printerStatus: 'connected',
          printerErrorCode: null,
          printerErrorMessage: '',
          forcePrint: false
        })

        // 如果之前有耗材不匹配的警告，重新检查
        if (that.data.materialMismatch) {
          that.checkMaterialCompatibility()
        }

        wx.showToast({
          title: '打印成功',
          icon: 'success'
        })
      } else if (statusInfo.isConnected) {
        // 设备仍然连接，但打印有问题
        that.setData({
          printerStatus: 'connected',
          printerErrorCode: res.ResultCode,
          printerErrorMessage: statusInfo.message
        })
        wx.showToast({
          title: statusInfo.message,
          icon: 'none',
          duration: 3000
        })
      } else {
        // 设备连接断开
        that.setData({
          printerStatus: 'error',
          printerErrorCode: res.ResultCode,
          printerErrorMessage: statusInfo.message
        })
        wx.showToast({
          title: statusInfo.message,
          icon: 'none',
          duration: 3000
        })
      }
    }).catch(error => {
      console.log('打印异常:', error)
      that.setData({
        isPrinting: false,
        printerStatus: 'error',
        printerErrorCode: 132,
        printerErrorMessage: '打印异常终止'
      })
      wx.showToast({
        title: '打印异常终止',
        icon: 'error'
      })
    })
  },

  /**
   * 停止打印
   */
  stopPrint() {
    if (!this.data.isPrinting) {
      wx.showToast({
        title: '当前没有打印任务',
        icon: 'none'
      })
      return
    }

    const that = this
    bleTool.stopPrint(res => {
      console.log('停止打印回调:', res)
      that.setData({
        isPrinting: false,
        printerStatus: 'connected',
        printerErrorCode: null,
        printerErrorMessage: ''
      })
      wx.showToast({
        title: '已停止打印',
        icon: 'success'
      })
    }).catch(error => {
      console.error('停止打印失败:', error)
      that.setData({
        isPrinting: false
      })
      wx.showToast({
        title: '停止打印失败',
        icon: 'error'
      })
    })
  },
  /**
   * 断开蓝牙
   * @CallIn index.wxml 中触发点击断开蓝牙按钮点击事件
   * @CallOut bleTool.disconnectBleDevice
   */
  disconnectDevice() {
    bleTool.disconnectBleDevice().then(res => {
      console.log(res);
    }).catch(err => {
      console.log(err);
    })
  },
  /**
   * 打开在线商城
   */
  openOnlineStore() {
    // 这里可以配置跳转到其他小程序
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 显示联系我们弹窗
   */
  showContact() {
    this.setData({
      showContactModal: true
    })
  },

  /**
   * 关闭联系我们弹窗
   */
  closeContact() {
    this.setData({
      showContactModal: false
    })
  },

  /**
   * 复制联系信息
   */
  copyContact(e) {
    const text = e.currentTarget.dataset.text
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制',
          icon: 'success'
        })
      }
    })
  }
})
