// printer.js
import bleTool from '../../SUPVANAPIT50PRO/BLETool.js'
import bleToothManage from '../../SUPVANAPIT50PRO/BLEToothManage.js'
import constants from '../../SUPVANAPIT50PRO/Constants.js'

Page({
  data: {
    // 打印机状态
    printerStatus: 'disconnected', // disconnected, error, connected
    printerDeviceSn: '',
    
    // 模版相关
    templates: [], // 模版列表
    selectedTemplateIndex: 0, // 当前选中的模版索引
    previewImagePath: '', // 预览图路径
    
    // 标签内容
    labelContent: {
      productName: '品名',
      operator: '操作人',
      date: '',
      copies: 1
    },
    
    // 蓝牙相关
    blueList: [],
    isScanning: false,
    showDeviceSelector: false,
    
    // 联系我们弹窗
    showContactModal: false,
    
    // Canvas相关
    templateWidth: 560,
    templateHeight: 302,
    barCodeWidth: 214,
    barCodeHeight: 72,
    qrCodeWidth: 20,
    qrCodeHeight: 20,
    pixelRatio: 2,
    canvasBarCode: null,
    canvasText: null
  },

  onLoad() {
    this.initPage()
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 设置当前日期
    const today = new Date()
    const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`

    // 加载上次保存的标签内容
    const savedContent = wx.getStorageSync('lastLabelContent')
    const labelContent = savedContent ? {
      ...savedContent,
      date: savedContent.date || dateStr // 如果没有保存日期，使用当前日期
    } : {
      productName: '品名',
      operator: '操作人',
      date: dateStr,
      copies: 1
    }

    this.setData({
      labelContent: labelContent,
      pixelRatio: wx.getWindowInfo().pixelRatio,
      canvasText: wx.createCanvasContext('Canvas', this),
      canvasBarCode: wx.createSelectorQuery().in(this)
    })

    // 加载模版配置
    this.loadTemplates()

    // 尝试连接上次使用的打印机
    this.tryConnectLastPrinter()
  },

  /**
   * 加载模版配置
   */
  loadTemplates() {
    // 从本地存储获取上次选中的模版索引
    const lastSelectedIndex = wx.getStorageSync('lastSelectedTemplateIndex') || 0

    // 尝试从网络加载模版配置文件，如果失败则使用默认配置
    this.loadTemplatesFromNetwork().then(templates => {
      this.setData({
        templates: templates,
        selectedTemplateIndex: lastSelectedIndex < templates.length ? lastSelectedIndex : 0
      })
      this.generatePreview()
    }).catch(() => {
      // 网络加载失败，使用默认配置
      const templates = [
      {
        "TemplateName": "40 * 30 mm",
        "Width": 40,
        "Height": 30,
        "Rotate": 0,
        "Copies": 1,
        "Density": 2,
        "HorizontalNum": 0,
        "VerticalNum": 0,
        "PaperType": 1,
        "Gap": 3,
        "Speed": 25,
        "FirstCut": 1,
        "CutType": 1,
        "DeviceSn": "T0145B",
        "ImageWidth": 40,
        "ImageHeight": 30,
        "PreviewPath": "https://img.alicdn.com/imgextra/i2/3907484842/O1CN01D2qTCv1ldghCs5vzc_!!3907484842.png",
        "DrawObjects": [
          {
            "AntiColor": false,
            "X": 2,
            "Y": 7,
            "Width": 35,
            "Height": 4,
            "Content": "品名|_____________________",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "3",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 9,
            "Y": 5.5,
            "Width": 28,
            "Height": 4,
            "Content": "品名",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "5",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 2,
            "Y": 15,
            "Width": 35,
            "Height": 4,
            "Content": "操作人|______________________",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "3",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 12,
            "Y": 13.5,
            "Width": 20,
            "Height": 4,
            "Content": "操作人",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "5",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 2,
            "Y": 23,
            "Width": 35,
            "Height": 4,
            "Content": "日期|______________________",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "3",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 9,
            "Y": 21.5,
            "Width": 23,
            "Height": 4,
            "Content": "2025-06-17",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "5",
            "Format": "TEXT",
            "Orientation": 0
          }
        ]
      },
      {
        "TemplateName": "50 * 30 mm",
        "Width": 50,
        "Height": 30,
        "Rotate": 0,
        "Copies": 1,
        "Density": 2,
        "HorizontalNum": 0,
        "VerticalNum": 0,
        "PaperType": 1,
        "Gap": 3,
        "Speed": 25,
        "FirstCut": 1,
        "CutType": 1,
        "DeviceSn": "T0145B",
        "ImageWidth": 50,
        "ImageHeight": 30,
        "PreviewPath": "https://img.alicdn.com/imgextra/i2/3907484842/O1CN01D2qTCv1ldghCs5vzc_!!3907484842.png",
        "DrawObjects": [
          {
            "AntiColor": false,
            "X": 2,
            "Y": 7,
            "Width": 45,
            "Height": 4,
            "Content": "品名|_____________________________",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "3",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 12,
            "Y": 5.5,
            "Width": 33,
            "Height": 4,
            "Content": "品名",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "5",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 2,
            "Y": 15,
            "Width": 45,
            "Height": 4,
            "Content": "操作人|____________________________",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "3",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 15,
            "Y": 13.5,
            "Width": 30,
            "Height": 4,
            "Content": "操作人",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "5",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 2,
            "Y": 23,
            "Width": 45,
            "Height": 4,
            "Content": "日期|____________________________",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "3",
            "Format": "TEXT",
            "Orientation": 0
          },
          {
            "AntiColor": false,
            "X": 12,
            "Y": 21.5,
            "Width": 33,
            "Height": 4,
            "Content": "2025-06-17",
            "FontName": "HarmonyOS Sans SC",
            "FontStyle": 2,
            "FontSize": "5",
            "Format": "TEXT",
            "Orientation": 0
          }
        ]
      }
    ]
    
      this.setData({
        templates: templates,
        selectedTemplateIndex: lastSelectedIndex < templates.length ? lastSelectedIndex : 0
      })
      this.generatePreview()
    })
  },

  /**
   * 从网络加载模版配置
   */
  loadTemplatesFromNetwork() {
    return new Promise((resolve, reject) => {
      // 这里可以从服务器加载配置文件
      // 目前使用本地默认配置
      reject('使用本地配置')
    })
  },

  /**
   * 尝试连接上次使用的打印机
   */
  tryConnectLastPrinter() {
    const lastPrinter = wx.getStorageSync('lastConnectedPrinter')
    if (lastPrinter) {
      this.setData({
        printerDeviceSn: lastPrinter.name || lastPrinter.deviceId
      })
      // 这里可以尝试连接上次的打印机
      // bleTool.connectBleDevice(lastPrinter)...
    }
  },

  /**
   * 选择模版
   */
  selectTemplate(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      selectedTemplateIndex: index
    })

    // 保存选择到本地存储
    wx.setStorageSync('lastSelectedTemplateIndex', index)

    // 重新生成预览图
    this.generatePreview()
  },

  /**
   * 生成预览图
   */
  generatePreview() {
    const template = this.data.templates[this.data.selectedTemplateIndex]
    if (!template) return

    // 创建临时模版对象，更新内容
    const tempTemplate = JSON.parse(JSON.stringify(template))
    tempTemplate.Copies = parseInt(this.data.labelContent.copies) || 1

    // 更新DrawObjects中的内容（只更新可编辑的内容，保留固定的标题和下划线）
    if (tempTemplate.DrawObjects && tempTemplate.DrawObjects.length >= 6) {
      // 更新品名内容（索引1）
      tempTemplate.DrawObjects[1].Content = this.data.labelContent.productName
      // 更新操作人内容（索引3）
      tempTemplate.DrawObjects[3].Content = this.data.labelContent.operator
      // 更新日期内容（索引5）
      tempTemplate.DrawObjects[5].Content = this.data.labelContent.date
    }

    const that = this
    bleToothManage.doDrawPreview(this.data.canvasText, [tempTemplate], this.data.canvasBarCode, res => {
      if (res.ResultCode == constants.globalResultCode.ResultCode100) {
        let resultValue = res.ResultValue
        this.setData({
          templateWidth: resultValue.width,
          templateHeight: resultValue.height,
          barCodeWidth: resultValue.barcodeWidth,
          barCodeHeight: resultValue.barcodeHeight,
          qrCodeWidth: resultValue.qrcodeWidth,
          qrCodeHeight: resultValue.qrcodeHeight,
        })
      } else if (res.ResultCode == constants.globalResultCode.ResultCodeSuccess) {
        if (res.ResultValue && res.ResultValue.previewList && res.ResultValue.previewList.length > 0) {
          that.setData({
            previewImagePath: res.ResultValue.previewList[0]
          })
        }
      }
    }).catch(error => {
      console.error('生成预览图失败:', error)
    })
  },

  /**
   * 保存标签内容到本地存储
   */
  saveLabelContent() {
    wx.setStorageSync('lastLabelContent', this.data.labelContent)
  },

  /**
   * 输入品名
   */
  onProductNameInput(e) {
    this.setData({
      'labelContent.productName': e.detail.value.slice(0, 20) // 限制20个字符
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 输入操作人
   */
  onOperatorInput(e) {
    this.setData({
      'labelContent.operator': e.detail.value.slice(0, 20) // 限制20个字符
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 选择日期
   */
  onDateChange(e) {
    this.setData({
      'labelContent.date': e.detail.value
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 输入打印份数
   */
  onCopiesInput(e) {
    let value = e.detail.value
    // 允许空值，用户可能正在删除内容
    if (value === '') {
      this.setData({
        'labelContent.copies': ''
      })
      return
    }

    let copies = parseInt(value) || 1
    copies = Math.max(1, Math.min(250, copies)) // 限制1-250
    this.setData({
      'labelContent.copies': copies
    })
    this.saveLabelContent() // 保存内容
    // 移除预览图刷新，因为份数与内容无关
  },

  /**
   * 增加打印份数
   */
  increaseCopies() {
    let copies = parseInt(this.data.labelContent.copies) || 1
    copies = Math.min(250, copies + 1) // 最大250
    this.setData({
      'labelContent.copies': copies
    })
    this.saveLabelContent() // 保存内容
    // 移除预览图刷新，因为份数与内容无关
  },

  /**
   * 减少打印份数
   */
  decreaseCopies() {
    let copies = parseInt(this.data.labelContent.copies) || 1
    copies = Math.max(1, copies - 1) // 最小1
    this.setData({
      'labelContent.copies': copies
    })
    this.saveLabelContent() // 保存内容
    // 移除预览图刷新，因为份数与内容无关
  },

  /**
   * 连接打印机/打印标签按钮
   */
  onPrintAction() {
    if (this.data.printerStatus === 'connected') {
      // 已连接，执行打印
      this.printLabel()
    } else {
      // 未连接，开始连接流程
      this.startConnectPrinter()
    }
  },

  /**
   * 开始连接打印机流程
   */
  startConnectPrinter() {
    // 检查蓝牙授权
    wx.authorize({
      scope: 'scope.bluetooth',
      success: () => {
        this.scanAndConnectPrinter()
      },
      fail: () => {
        wx.showModal({
          title: '需要蓝牙权限',
          content: '请授权蓝牙功能以连接打印机',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting()
            }
          }
        })
      }
    })
  },

  /**
   * 扫描并连接打印机
   */
  scanAndConnectPrinter() {
    this.setData({
      isScanning: true,
      blueList: []
    })

    const that = this
    let scanTimeout = null

    // 2秒后检查扫描结果
    scanTimeout = setTimeout(() => {
      bleTool.stopScanBleDevices()
      that.setData({ isScanning: false })

      if (that.data.blueList.length === 1) {
        // 只有一台设备，直接连接
        that.connectDevice(that.data.blueList[0])
      } else if (that.data.blueList.length > 1) {
        // 多台设备，显示选择对话框
        that.setData({ showDeviceSelector: true })
      } else {
        // 没有找到设备
        that.setData({
          printerStatus: 'error'
        })
        wx.showToast({
          title: '未找到可用的打印机',
          icon: 'none'
        })
      }
    }, 2000)

    // 开始扫描
    bleTool.scanBleDeviceList((res) => {
      if (res.ResultCode == 0) {
        if (res && res.ResultValue && res.ResultValue.devices[0]) {
          that.data.blueList.push(res.ResultValue.devices[0])
          that.setData({
            blueList: that.data.blueList
          })
        }
      }
    }).catch(error => {
      console.log('搜索蓝牙设备失败:', error)
      that.setData({ isScanning: false })
      clearTimeout(scanTimeout)
    })
  },

  /**
   * 连接指定设备
   */
  connectDevice(device) {
    const that = this
    bleTool.connectBleDevice(device).then(res => {
      console.log('连接成功:', res)
      that.setData({
        printerStatus: 'connected',
        printerDeviceSn: device.name || device.deviceId,
        showDeviceSelector: false
      })

      // 保存到本地存储
      wx.setStorageSync('lastConnectedPrinter', device)

      // 更新模版的DeviceSn
      const templates = that.data.templates
      templates.forEach(template => {
        template.DeviceSn = device.name || device.deviceId
      })
      that.setData({ templates })

      wx.showToast({
        title: '连接成功',
        icon: 'success'
      })
    }).catch(error => {
      console.log('连接失败:', error)
      that.setData({
        printerStatus: 'error'
      })
      wx.showToast({
        title: '连接失败',
        icon: 'error'
      })
    })
  },

  /**
   * 选择设备对话框中的设备选择
   */
  selectDevice(e) {
    const index = e.currentTarget.dataset.index
    const device = this.data.blueList[index]
    this.connectDevice(device)
  },

  /**
   * 关闭设备选择对话框
   */
  closeDeviceSelector() {
    this.setData({
      showDeviceSelector: false
    })
  },

  /**
   * 打印标签
   */
  printLabel() {
    const template = this.data.templates[this.data.selectedTemplateIndex]
    if (!template) {
      wx.showToast({
        title: '请选择模版',
        icon: 'none'
      })
      return
    }

    // 创建打印模版对象
    const printTemplate = JSON.parse(JSON.stringify(template))
    printTemplate.Copies = parseInt(this.data.labelContent.copies) || 1

    // 更新DrawObjects中的内容（只更新可编辑的内容，保留固定的标题和下划线）
    if (printTemplate.DrawObjects && printTemplate.DrawObjects.length >= 6) {
      // 更新品名内容（索引1）
      printTemplate.DrawObjects[1].Content = this.data.labelContent.productName
      // 更新操作人内容（索引3）
      printTemplate.DrawObjects[3].Content = this.data.labelContent.operator
      // 更新日期内容（索引5）
      printTemplate.DrawObjects[5].Content = this.data.labelContent.date
    }

    // 执行打印
    bleToothManage.doPrintMatrix(this.data.canvasText, [printTemplate], this.data.canvasBarCode, res => {
      console.log('打印回调:', res)
      if (res.ResultCode == constants.globalResultCode.ResultCode100) {
        wx.showToast({
          title: '打印成功',
          icon: 'success'
        })
      }
    }).catch(error => {
      console.log('打印失败:', error)
      wx.showToast({
        title: '打印失败',
        icon: 'error'
      })
    })
  },

  /**
   * 打开在线商城
   */
  openOnlineStore() {
    // 这里可以配置跳转到其他小程序
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 显示联系我们弹窗
   */
  showContact() {
    this.setData({
      showContactModal: true
    })
  },

  /**
   * 关闭联系我们弹窗
   */
  closeContact() {
    this.setData({
      showContactModal: false
    })
  },

  /**
   * 复制联系信息
   */
  copyContact(e) {
    const text = e.currentTarget.dataset.text
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制',
          icon: 'success'
        })
      }
    })
  }
})
